import mongoose, { Document, Schema } from 'mongoose';

export interface Transaction extends Document {
  userId: mongoose.Types.ObjectId;
  amount: number; 
  type: 'subscription_payment' | 'lesson_payout' | 'refund' | 'fee';
  status: 'pending' | 'completed' | 'failed';
  stripeTransactionId?: string;
  description?: string;
  createdAt: Date;
}

const transactionSchema = new Schema<Transaction>({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  amount: { type: Number, required: true },
  type: { 
    type: String, 
    enum: ['subscription_payment', 'lesson_payout', 'refund', 'fee'], 
    required: true 
  },
  status: { 
    type: String, 
    enum: ['pending', 'completed', 'failed'], 
    default: 'pending' 
  },
  stripeTransactionId: { type: String },
  description: { type: String },
  createdAt: { type: Date, default: Date.now }
});

const TransactionModel = mongoose.model<Transaction>('Transaction', transactionSchema);

export default TransactionModel;
