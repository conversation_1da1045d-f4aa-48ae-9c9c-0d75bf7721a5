import { Schema, model, Document, Types } from 'mongoose';

// Interface for individual time slots within a day
export interface ITimeSlot {
  startTime: string; // "09:00" format
  endTime: string;   // "10:00" format
  isAvailable: boolean;
  maxStudents?: number; // For group sessions
  sessionType?: 'lesson' | 'consultation' | 'interview' | 'break';
  price?: number; // Override default tutor price for specific slots
  notes?: string;
}

// Interface for daily schedule
export interface IDaySchedule {
  dayOfWeek: number; // 0=Sunday, 1=Monday, ..., 6=Saturday
  isWorkingDay: boolean;
  timeSlots: ITimeSlot[];
  breakTimes?: {
    startTime: string;
    endTime: string;
    title?: string;
  }[];
  dayNotes?: string;
}

// Interface for schedule exceptions (holidays, vacations, etc.)
export interface IScheduleException {
  date: Date;
  type: 'holiday' | 'vacation' | 'sick' | 'unavailable' | 'custom';
  title: string;
  description?: string;
  isRecurring?: boolean; // For annual holidays
  replacementSchedule?: IDaySchedule; // Alternative schedule for this day
}

// Main Schedule interface
export interface ISchedule extends Document {
  tutorId: Types.ObjectId;
  calendarId: Types.ObjectId;
  name: string;
  description?: string;
  timezone: string;
  isActive: boolean;
  isDefault: boolean; // Default schedule for the tutor

  // Weekly recurring schedule
  weeklySchedule: IDaySchedule[];

  // Schedule exceptions and overrides
  exceptions: IScheduleException[];

  // Schedule settings
  settings: {
    autoGenerateEvents: boolean; // Automatically create events from schedule
    advanceGenerationDays: number; // How many days ahead to generate events
    defaultSessionDuration: number; // Default duration in minutes
    bufferTimeBetweenSessions: number; // Buffer time in minutes
    allowBackToBackBookings: boolean;
    maxDailyBookings?: number;
    requireBreakAfterConsecutive?: number; // Require break after X consecutive sessions
  };

  // Metadata
  effectiveFrom: Date;
  effectiveTo?: Date;
  lastEventGeneration?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getScheduleForDate(date: Date): IDaySchedule | null;
  isAvailableAt(date: Date, time: string): boolean;
  getAvailableSlots(date: Date): ITimeSlot[];
  needsEventGeneration(): boolean;
}

// Time slot schema
const timeSlotSchema = new Schema<ITimeSlot>({
  startTime: { type: String, required: true, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
  endTime: { type: String, required: true, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
  isAvailable: { type: Boolean, default: true },
  maxStudents: { type: Number, default: 1, min: 1 },
  sessionType: { 
    type: String, 
    enum: ['lesson', 'consultation', 'interview', 'break'], 
    default: 'lesson' 
  },
  price: { type: Number, min: 0 },
  notes: String
}, { _id: false });

// Day schedule schema
const dayScheduleSchema = new Schema<IDaySchedule>({
  dayOfWeek: { type: Number, required: true, min: 0, max: 6 },
  isWorkingDay: { type: Boolean, default: true },
  timeSlots: [timeSlotSchema],
  breakTimes: [{
    startTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
    endTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
    title: String
  }],
  dayNotes: String
}, { _id: false });

// Schedule exception schema
const scheduleExceptionSchema = new Schema<IScheduleException>({
  date: { type: Date, required: true },
  type: { 
    type: String, 
    enum: ['holiday', 'vacation', 'sick', 'unavailable', 'custom'], 
    required: true 
  },
  title: { type: String, required: true },
  description: String,
  isRecurring: { type: Boolean, default: false },
  replacementSchedule: dayScheduleSchema
}, { _id: false });

// Main schedule schema
const scheduleSchema = new Schema<ISchedule>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  calendarId: { type: Schema.Types.ObjectId, ref: 'Calendar', required: true },
  name: { type: String, required: true },
  description: String,
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },
  isDefault: { type: Boolean, default: false },
  
  weeklySchedule: [dayScheduleSchema],
  exceptions: [scheduleExceptionSchema],
  
  settings: {
    autoGenerateEvents: { type: Boolean, default: true },
    advanceGenerationDays: { type: Number, default: 30, min: 1, max: 365 },
    defaultSessionDuration: { type: Number, default: 60, min: 15 },
    bufferTimeBetweenSessions: { type: Number, default: 15, min: 0 },
    allowBackToBackBookings: { type: Boolean, default: false },
    maxDailyBookings: { type: Number, min: 1 },
    requireBreakAfterConsecutive: { type: Number, min: 2 }
  },
  
  effectiveFrom: { type: Date, default: () => new Date() },
  effectiveTo: Date,
  lastEventGeneration: Date,
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Indexes for better query performance
scheduleSchema.index({ tutorId: 1 });
scheduleSchema.index({ calendarId: 1 });
scheduleSchema.index({ tutorId: 1, isActive: 1 });
scheduleSchema.index({ tutorId: 1, isDefault: 1 });
scheduleSchema.index({ effectiveFrom: 1, effectiveTo: 1 });
scheduleSchema.index({ 'exceptions.date': 1 });

// Pre-save middleware
scheduleSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Validate that only one default schedule exists per tutor
  if (this.isDefault && this.isModified('isDefault')) {
    Schedule.updateMany(
      { tutorId: this.tutorId, _id: { $ne: this._id } },
      { isDefault: false }
    ).exec();
  }
  
  // Validate time slots don't overlap within a day
  for (const daySchedule of this.weeklySchedule) {
    const slots = daySchedule.timeSlots.sort((a, b) => a.startTime.localeCompare(b.startTime));
    for (let i = 0; i < slots.length - 1; i++) {
      if (slots[i].endTime > slots[i + 1].startTime) {
        return next(new Error(`Overlapping time slots on ${getDayName(daySchedule.dayOfWeek)}`));
      }
    }
  }
  
  next();
});

// Instance methods
scheduleSchema.methods.getScheduleForDate = function(date: Date): IDaySchedule | null {
  const dayOfWeek = date.getDay();
  
  // Check for exceptions first
  const exception = this.exceptions.find((exc: IScheduleException) => {
    if (exc.isRecurring) {
      return exc.date.getMonth() === date.getMonth() && exc.date.getDate() === date.getDate();
    }
    return exc.date.toDateString() === date.toDateString();
  });
  
  if (exception) {
    if (exception.type === 'unavailable' || exception.type === 'holiday' || exception.type === 'sick') {
      return null; // No availability
    }
    if (exception.replacementSchedule) {
      return exception.replacementSchedule;
    }
  }
  
  // Return regular weekly schedule
  return this.weeklySchedule.find((day: IDaySchedule) => day.dayOfWeek === dayOfWeek) || null;
};

scheduleSchema.methods.isAvailableAt = function(date: Date, time: string): boolean {
  const daySchedule = this.getScheduleForDate(date);
  if (!daySchedule || !daySchedule.isWorkingDay) return false;
  
  return daySchedule.timeSlots.some((slot: ITimeSlot) => 
    slot.isAvailable && 
    slot.sessionType !== 'break' &&
    time >= slot.startTime && 
    time < slot.endTime
  );
};

scheduleSchema.methods.getAvailableSlots = function(date: Date): ITimeSlot[] {
  const daySchedule = this.getScheduleForDate(date);
  if (!daySchedule || !daySchedule.isWorkingDay) return [];
  
  return daySchedule.timeSlots.filter((slot: ITimeSlot) => 
    slot.isAvailable && slot.sessionType !== 'break'
  );
};

scheduleSchema.methods.needsEventGeneration = function(): boolean {
  if (!this.settings.autoGenerateEvents) return false;
  
  const now = new Date();
  const lastGeneration = this.lastEventGeneration || new Date(0);
  const daysSinceLastGeneration = Math.floor((now.getTime() - lastGeneration.getTime()) / (1000 * 60 * 60 * 24));
  
  return daysSinceLastGeneration >= 1; // Generate daily
};

// Helper function
function getDayName(dayOfWeek: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayOfWeek];
}

export const Schedule = model<ISchedule>('Schedule', scheduleSchema);
