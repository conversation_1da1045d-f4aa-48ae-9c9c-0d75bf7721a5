import { Schema, model, Document, Types } from 'mongoose';

export interface ITrialBooking extends Document {
  studentId: Types.ObjectId;
  tutorId: Types.ObjectId;
  bookingId?: Types.ObjectId; // Reference to the actual booking
  trialType: 'lesson' | 'interview' | 'consultation';
  status: 'used' | 'completed' | 'cancelled' | 'no_show';
  usedAt: Date;
  completedAt?: Date;
  feedback?: {
    rating?: number;
    comment?: string;
    wouldRecommend?: boolean;
  };
  metadata?: {
    duration?: number; // in minutes
    subject?: string;
    notes?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const trialBookingSchema = new Schema<ITrialBooking>({
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  bookingId: { type: Schema.Types.ObjectId, ref: 'Booking' },
  trialType: { 
    type: String, 
    enum: ['lesson', 'interview', 'consultation'], 
    default: 'lesson' 
  },
  status: { 
    type: String, 
    enum: ['used', 'completed', 'cancelled', 'no_show'], 
    default: 'used' 
  },
  usedAt: { type: Date, required: true },
  completedAt: Date,
  feedback: {
    rating: { type: Number, min: 1, max: 5 },
    comment: String,
    wouldRecommend: Boolean
  },
  metadata: {
    duration: Number,
    subject: String,
    notes: String
  },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
trialBookingSchema.index({ studentId: 1, tutorId: 1 });
trialBookingSchema.index({ studentId: 1, status: 1 });
trialBookingSchema.index({ tutorId: 1, status: 1 });
trialBookingSchema.index({ usedAt: -1 });

trialBookingSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static methods
trialBookingSchema.statics.hasUsedTrialWithTutor = async function(
  studentId: Types.ObjectId, 
  tutorId: Types.ObjectId, 
  trialType: string = 'lesson'
): Promise<boolean> {
  const count = await this.countDocuments({
    studentId,
    tutorId,
    trialType,
    status: { $in: ['used', 'completed'] }
  });
  return count > 0;
};

trialBookingSchema.statics.getTrialHistory = async function(
  studentId: Types.ObjectId,
  tutorId?: Types.ObjectId
): Promise<ITrialBooking[]> {
  const query: any = { studentId };
  if (tutorId) {
    query.tutorId = tutorId;
  }
  
  return this.find(query)
    .populate('tutorId', 'firstname lastname email avatar')
    .populate('bookingId', 'eventId status')
    .sort({ usedAt: -1 });
};

trialBookingSchema.statics.canUseTrialWithTutor = async function(
  studentId: Types.ObjectId,
  tutorId: Types.ObjectId,
  trialType: string = 'lesson'
): Promise<{
  canUseTrial: boolean;
  reason?: string;
  previousTrials?: number;
}> {
  const previousTrials = await this.countDocuments({
    studentId,
    tutorId,
    trialType,
    status: { $in: ['used', 'completed'] }
  });

  // Allow one trial per tutor per trial type
  if (previousTrials > 0) {
    return {
      canUseTrial: false,
      reason: `You have already used your ${trialType} trial with this tutor`,
      previousTrials
    };
  }

  return {
    canUseTrial: true,
    previousTrials
  };
};

// Instance methods
trialBookingSchema.methods.markCompleted = function(feedback?: any): Promise<ITrialBooking> {
  this.status = 'completed';
  this.completedAt = new Date();
  if (feedback) {
    this.feedback = { ...this.feedback, ...feedback };
  }
  return this.save();
};

trialBookingSchema.methods.markCancelled = function(): Promise<ITrialBooking> {
  this.status = 'cancelled';
  return this.save();
};

export const TrialBooking = model<ITrialBooking>('TrialBooking', trialBookingSchema);
