import mongoose, { Schema, Document } from "mongoose";
import { IProfile, ProfileSchema, SUB_SCHEMA_OPTS } from "./profile";
import { FileProps, FileSchema } from "./file";
import { normalizeDatetime } from "../utils/datetime";

export interface IAvailability {
  day: string;
  startTime: string;
  endTime: string;
}

type SubjectProps = {
  id: string;
  title: string;
  experienceLevel: string;
  qualities: string[];
  specialities: string[];
};

type CertificateProps = {
  id: string;
  title: string;
  subject: string;
  startDate: Date;
  endDate: Date;
  file: FileProps;
};

type AcademicProps = {
  id: string;
  university: string;
  degree: string;
  degreeType: string;
  startDate: Date;
  endDate: Date;
  file: FileProps;
};

export interface ITutor extends Document, IProfile {
  role: "tutor";
  teachingSubjects: SubjectProps[];
  certificates: CertificateProps[];
  academics: AcademicProps[];
  teachingExperience: string;
  motivatePotentialStudent: string;
  headline: string;
  introVideo: string;
  basePrice: number;
  approvalStatus: "pending" | "approved" | "rejected";
  isFlagged: boolean;
  reviews: { content: string; flagged: boolean }[];
  isActive: boolean;
  rating: number;
  totalLessons: number;
  availableBalance: number;
}

const SubjectSchema = new Schema(
  {
    title: { type: String, required: "Subject title is required" },
    experienceLevel: { type: String },
    qualities: { type: [String] },
    specialities: { type: [String] },
    createdAt: { type: Date, default: normalizeDatetime() },
    updatedAt: Date,
  },
  SUB_SCHEMA_OPTS
);

const CertificateSchema = new Schema(
  {
    title: { type: String, required: "Certificate title is required" },
    subject: { type: String, required: "Certificate subject is required" },
    startDate: { type: Date, required: "Certificate start year is required" },
    endDate: { type: Date, required: "Certificate end year is required" },
    file: { type: FileSchema },
    createdAt: { type: Date, default: normalizeDatetime() },
    updatedAt: Date,
  },
  SUB_SCHEMA_OPTS
);

const AcademicSchema = new Schema(
  {
    university: { type: String, required: "Academic is required" },
    degree: { type: String, required: "Degree is required" },
    degreeType: { type: String, required: "Degree type is required" },
    startDate: { type: Date, required: "Academic start year is required" },
    endDate: { type: Date, required: "Academic end year is required" },
    file: { type: FileSchema },
    createdAt: { type: Date, default: normalizeDatetime() },
    updatedAt: Date,
  },
  SUB_SCHEMA_OPTS
);

const TutorSchema: Schema = new Schema({
  teachingSubjects: [
    {
      type: SubjectSchema,
      // required: [true, "Atleast 1 teaching subject is required"],
    },
  ],
  academics: [
    {
      type: AcademicSchema,
      // required: [true, "Atleast 1 academic details is required"],
    },
  ],
  teachingExperience: {
    type: String,
    // required: [true, "Teaching experience is required"],
  },
  motivatePotentialStudent: {
    type: String,
    // required: [true, "Student motivational text is required"],
  },
  headline: {
    type: String,
    // required: [true, "Headline about you is required"],
  },
  introVideo: {
    type: String,
    // required: [true, "Intro video is required"],
  },
  basePrice: {
    type: Number,
    // required: [true, "Base price is required"],
  },
  certificates: [{ type: CertificateSchema }],
  approvalStatus: { type: String, default: "pending" },
  isFlagged: { type: Boolean, default: false },
  reviews: [
    {
      content: String,
      flagged: { type: Boolean, default: false },
    },
  ],
  isActive: { type: Boolean, default: true },
  rating: { type: Number, default: 0, min: 0, max: 5 },
  totalLessons: { type: Number, default: 0, min: 0 },
  availableBalance: { type: Number, default: 0, min: 0 },
});

TutorSchema.add(ProfileSchema);

// Virtuals
TutorSchema.virtual("user", {
  ref: "User",
  localField: "userId",
  foreignField: "_id",
  justOne: true,
});

TutorSchema.virtual("subscriptions", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "tutorId",
});

TutorSchema.virtual("lessons", {
  ref: "Lesson",
  localField: "_id",
  foreignField: "tutorId",
});

TutorSchema.virtual("withdrawalRequests", {
  ref: "WithdrawalRequest",
  localField: "_id",
  foreignField: "tutorId",
});

// Methods
TutorSchema.methods.canWithdraw = function (amount: number): boolean {
  return this.availableBalance >= amount;
};

// Serialize virtuals
TutorSchema.set("toJSON", { virtuals: true });
TutorSchema.set("toObject", { virtuals: true });

const Tutor = mongoose.model<ITutor>("Tutor", TutorSchema);

export default Tutor;
