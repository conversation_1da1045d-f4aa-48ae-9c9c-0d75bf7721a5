import { Schema, model, Document, Types } from 'mongoose';

export interface IEvent extends Document {
        calendarId: Types.ObjectId;
        tutorId: Types.ObjectId; // The tutor who owns this event
        title: string;
        description?: string;
        location?: string;
        startDateTime: Date;
        endDateTime: Date;
        allDay?: boolean;
        status?: 'available' | 'booked' | 'cancelled' | 'completed';
        visibility?: 'public' | 'private' | 'confidential';
        priority?: number;
        isRecurring?: boolean;
        recurringPattern?: {
                frequency: 'daily' | 'weekly' | 'monthly';
                interval: number; // Every X days/weeks/months
                daysOfWeek?: number[]; // For weekly: 0=Sunday, 1=Monday, etc.
                endDate?: Date;
                maxOccurrences?: number;
        };
        bookingInfo?: {
                maxStudents?: number; // For group sessions
                currentBookings?: number;
                requiresApproval?: boolean;
                price?: number; // If different from tutor's base price
        };
        createdAt: Date;
        updatedAt: Date;
}

const eventSchema = new Schema<IEvent>({
    calendarId: { type: Schema.Types.ObjectId, ref: 'Calendar', required: true },
    tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
    title: { type: String, required: true },
    description: String,
    location: String,
    startDateTime: { type: Date, required: true },
    endDateTime: { type: Date, required: true },
    allDay: { type: Boolean, default: false },
    status: { type: String, enum: ['available', 'booked', 'cancelled', 'completed'], default: 'available' },
    visibility: { type: String, enum: ['public', 'private', 'confidential'], default: 'public' },
    priority: { type: Number, default: 3 },
    isRecurring: { type: Boolean, default: false },
    recurringPattern: {
        frequency: { type: String, enum: ['daily', 'weekly', 'monthly'] },
        interval: { type: Number, min: 1 },
        daysOfWeek: [{ type: Number, min: 0, max: 6 }],
        endDate: Date,
        maxOccurrences: { type: Number, min: 1 }
    },
    bookingInfo: {
        maxStudents: { type: Number, default: 1, min: 1 },
        currentBookings: { type: Number, default: 0, min: 0 },
        requiresApproval: { type: Boolean, default: false },
        price: { type: Number, min: 0 }
    },
    createdAt: { type: Date, default: () => new Date() },
    updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
eventSchema.index({ calendarId: 1 });
eventSchema.index({ tutorId: 1 });
eventSchema.index({ startDateTime: 1, endDateTime: 1 });
eventSchema.index({ status: 1 });
eventSchema.index({ tutorId: 1, status: 1, startDateTime: 1 });

// Pre-save middleware
eventSchema.pre('save', function(next) {
    this.updatedAt = new Date();

    // Validate that end time is after start time
    if (this.endDateTime <= this.startDateTime) {
        return next(new Error('End time must be after start time'));
    }

    // Ensure current bookings doesn't exceed max students
    if (this.bookingInfo?.currentBookings && this.bookingInfo?.maxStudents) {
        if (this.bookingInfo.currentBookings > this.bookingInfo.maxStudents) {
            return next(new Error('Current bookings cannot exceed maximum students'));
        }
    }

    next();
});

// Instance methods
eventSchema.methods.isAvailableForBooking = function(): boolean {
    const now = new Date();
    return this.status === 'available' &&
           this.startDateTime > now &&
           (this.bookingInfo?.currentBookings || 0) < (this.bookingInfo?.maxStudents || 1);
};

eventSchema.methods.canAcceptMoreBookings = function(): boolean {
    return (this.bookingInfo?.currentBookings || 0) < (this.bookingInfo?.maxStudents || 1);
};

eventSchema.methods.getDurationInMinutes = function(): number {
    return Math.floor((this.endDateTime.getTime() - this.startDateTime.getTime()) / (1000 * 60));
};

export const Event = model<IEvent>('Event', eventSchema);
