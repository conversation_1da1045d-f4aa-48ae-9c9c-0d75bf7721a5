import { Schema, model, Document, Types } from 'mongoose';

export interface IStudentCalendar extends Document {
  studentId: Types.ObjectId;
  name: string;
  description?: string;
  color?: string;
  isDefault?: boolean; // Default calendar for the student
  timezone?: string;
  isActive?: boolean;
  preferences?: {
    defaultView?: 'month' | 'week' | 'day' | 'agenda';
    showTutorCalendars?: boolean;
    reminderSettings?: {
      enabled?: boolean;
      minutesBefore?: number[];
    };
    availabilitySettings?: {
      preferredTimeSlots?: {
        dayOfWeek: number; // 0=Sunday, 1=Monday, etc.
        startTime: string; // "09:00"
        endTime: string; // "17:00"
      }[];
      blockedTimeSlots?: {
        dayOfWeek: number;
        startTime: string;
        endTime: string;
        reason?: string;
      }[];
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

const studentCalendarSchema = new Schema<IStudentCalendar>({
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  name: { type: String, required: true },
  description: String,
  color: { type: String, default: '#4F46E5' }, // Indigo color
  isDefault: { type: Boolean, default: false },
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },
  preferences: {
    defaultView: { type: String, enum: ['month', 'week', 'day', 'agenda'], default: 'month' },
    showTutorCalendars: { type: Boolean, default: true },
    reminderSettings: {
      enabled: { type: Boolean, default: true },
      minutesBefore: { type: [Number], default: [15, 60] } // 15 minutes and 1 hour before
    },
    availabilitySettings: {
      preferredTimeSlots: [{
        dayOfWeek: { type: Number, min: 0, max: 6 },
        startTime: String,
        endTime: String
      }],
      blockedTimeSlots: [{
        dayOfWeek: { type: Number, min: 0, max: 6 },
        startTime: String,
        endTime: String,
        reason: String
      }]
    }
  },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
studentCalendarSchema.index({ studentId: 1 });
studentCalendarSchema.index({ studentId: 1, isActive: 1 });
studentCalendarSchema.index({ studentId: 1, isDefault: 1 });

studentCalendarSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Ensure only one default calendar per student
studentCalendarSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    await StudentCalendar.updateMany(
      { studentId: this.studentId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Instance methods
studentCalendarSchema.methods.isWithinPreferredHours = function(dateTime: Date): boolean {
  const dayOfWeek = dateTime.getDay();
  const timeString = dateTime.toTimeString().substring(0, 5); // "HH:MM"
  
  const preferredSlots = this.preferences?.availabilitySettings?.preferredTimeSlots || [];
  return preferredSlots.some((slot: any) =>
    slot.dayOfWeek === dayOfWeek &&
    timeString >= slot.startTime &&
    timeString <= slot.endTime
  );
};

studentCalendarSchema.methods.isBlocked = function(dateTime: Date): boolean {
  const dayOfWeek = dateTime.getDay();
  const timeString = dateTime.toTimeString().substring(0, 5); // "HH:MM"
  
  const blockedSlots = this.preferences?.availabilitySettings?.blockedTimeSlots || [];
  return blockedSlots.some((slot: any) =>
    slot.dayOfWeek === dayOfWeek &&
    timeString >= slot.startTime &&
    timeString <= slot.endTime
  );
};

export const StudentCalendar = model<IStudentCalendar>('StudentCalendar', studentCalendarSchema);
