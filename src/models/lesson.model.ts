import mongoose, { Document, Schema } from "mongoose";

export interface Lesson extends Document {
  tutorId: mongoose.Types.ObjectId;
  studentId: mongoose.Types.ObjectId;
  subscriptionId: mongoose.Types.ObjectId;
  scheduledTime: Date;
  duration: number; // in minutes
  status: "scheduled" | "completed" | "cancelled" | "no-show";
  confirmedAt?: Date;
  cancelledAt?: Date;
  cancellationReason?: string;
  notes?: string;
  createdAt: Date;
}

const lessonSchema = new Schema<Lesson>({
  tutorId: { type: Schema.Types.ObjectId, ref: "Tutor", required: true },
  studentId: { type: Schema.Types.ObjectId, ref: "Student", required: true },
  subscriptionId: {
    type: Schema.Types.ObjectId,
    ref: "Subscription",
    required: true,
  },
  scheduledTime: { type: Date, required: true },
  duration: { type: Number, default: 50 }, // minutes
  status: {
    type: String,
    enum: ["scheduled", "completed", "cancelled", "no-show"],
    default: "scheduled",
  },
  confirmedAt: { type: Date },
  cancelledAt: { type: Date },
  cancellationReason: { type: String },
  notes: { type: String },
  createdAt: { type: Date, default: Date.now },
});

const LessonModel = mongoose.model<Lesson>("Lesson", lessonSchema);
export default LessonModel;
