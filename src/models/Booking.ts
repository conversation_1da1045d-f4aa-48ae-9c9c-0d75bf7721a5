import { Schema, model, Document, Types } from 'mongoose';

export interface IBooking extends Document {
  eventId: Types.ObjectId;       // The tutor's event/session
  studentId: Types.ObjectId;     // The student booking the session
  tutorId: Types.ObjectId;       // The tutor (for easier queries)
  subscriptionId: Types.ObjectId; // The subscription that allows this booking
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'no_show';
  bookingNotes?: string;         // Student's notes for the booking
  tutorNotes?: string;           // Tutor's notes about the session
  cancellationReason?: string;   // Reason for cancellation
  cancelledBy?: 'student' | 'tutor' | 'system';
  cancelledAt?: Date;
  confirmedAt?: Date;
  completedAt?: Date;
  reminderSent?: boolean;        // Whether reminder was sent
  createdAt: Date;
  updatedAt: Date;
}

const bookingSchema = new Schema<IBooking>({
  eventId: { type: Schema.Types.ObjectId, ref: 'Event', required: true },
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  subscriptionId: { type: Schema.Types.ObjectId, ref: 'Subscription', required: true },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'],
    default: 'pending'
  },
  bookingNotes: String,
  tutorNotes: String,
  cancellationReason: String,
  cancelledBy: { type: String, enum: ['student', 'tutor', 'system'] },
  cancelledAt: Date,
  confirmedAt: Date,
  completedAt: Date,
  reminderSent: { type: Boolean, default: false },
  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
bookingSchema.index({ eventId: 1 });
bookingSchema.index({ studentId: 1 });
bookingSchema.index({ tutorId: 1 });
bookingSchema.index({ subscriptionId: 1 });
bookingSchema.index({ status: 1 });
bookingSchema.index({ studentId: 1, status: 1 });
bookingSchema.index({ tutorId: 1, status: 1 });
bookingSchema.index({ createdAt: -1 });

// Pre-save middleware
bookingSchema.pre('save', function(next) {
    this.updatedAt = new Date();

    // Set timestamps based on status changes
    if (this.isModified('status')) {
        const now = new Date();
        switch (this.status) {
            case 'confirmed':
                if (!this.confirmedAt) this.confirmedAt = now;
                break;
            case 'cancelled':
                if (!this.cancelledAt) this.cancelledAt = now;
                break;
            case 'completed':
                if (!this.completedAt) this.completedAt = now;
                break;
        }
    }

    next();
});

// Instance methods
bookingSchema.methods.canBeCancelled = function(): boolean {
    return ['pending', 'confirmed'].includes(this.status);
};

bookingSchema.methods.canBeConfirmed = function(): boolean {
    return this.status === 'pending';
};

bookingSchema.methods.isActive = function(): boolean {
    return ['pending', 'confirmed'].includes(this.status);
};

export const Booking = model<IBooking>('Booking', bookingSchema);
