// models/Classroom.ts
import mongoose, { Document, Schema } from "mongoose";

export interface IClassroom extends Document {
  tutor: mongoose.Types.ObjectId;
  learner: mongoose.Types.ObjectId;
  channelId: string;
  startedAt?: Date;
  endedAt?: Date;
  tutorJoinedAt?: Date;
  tutorLeftAt?: Date;
  learnerJoinedAt?: Date;
  learnerLeftAt?: Date;
  status: "scheduled" | "in_progress" | "completed" | "cancelled";
  notes?: string;
  rating?: number;
  createdAt: Date;
  updatedAt: Date;
}

const ClassroomSchema = new Schema<IClassroom>(
  {
    tutor: { type: Schema.Types.ObjectId, ref: "Profile", required: true },
    learner: { type: Schema.Types.ObjectId, ref: "Profile", required: true },
    channelId: { type: String, required: true },
    tutorJoinedAt: Date,
    tutorLeftAt: Date,
    learnerJoinedAt: Date,
    learnerLeftAt: Date,
    startedAt: Date,
    endedAt: Date,
    status: {
      type: String,
      enum: ["scheduled", "in_progress", "completed", "cancelled"],
      default: "scheduled",
    },
    notes: String,
    rating: Number,
  },
  { timestamps: true }
);

const Classroom = mongoose.model<IClassroom>("Classroom", ClassroomSchema);
export default Classroom;
