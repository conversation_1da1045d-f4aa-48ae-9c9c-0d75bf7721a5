import { Schema, model, Document, Types } from 'mongoose';

export interface ICalendar extends Document {
  ownerId: Types.ObjectId; // Can be student or tutor
  ownerType: 'student' | 'tutor';
  name: string;
  description?: string;
  color?: string;
  isShared?: boolean; // whether this calendar is visible to others
  timezone?: string; // Owner's timezone for proper scheduling
  isActive?: boolean; // Whether the calendar is currently active

  // Schedule integration
  hasSchedule?: boolean; // Whether this calendar uses schedule-based availability
  defaultScheduleId?: Types.ObjectId; // Reference to default schedule
  scheduleSettings?: {
    autoGenerateFromSchedule?: boolean; // Auto-generate events from schedule
    generateDaysAhead?: number; // How many days ahead to generate
    allowScheduleOverrides?: boolean; // Allow manual event overrides
  };



  // Display settings
  displaySettings?: {
    showSchedulePattern?: boolean; // Show underlying schedule pattern in calendar view
    showAvailabilityOnly?: boolean; // Only show available slots to students
    groupSimilarSlots?: boolean; // Group similar time slots for cleaner display
  };

  createdAt: Date;
  updatedAt: Date;
}

const calendarSchema = new Schema<ICalendar>({
  ownerId: { type: Schema.Types.ObjectId, required: true },
  ownerType: { type: String, enum: ['student', 'tutor'], required: true },
  name: { type: String, required: true },
  description: String,
  color: { type: String, default: '#3B82F6' }, // Blue color
  isShared: { type: Boolean, default: true }, // Default to shared
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },

  // Schedule integration
  hasSchedule: { type: Boolean, default: false },
  defaultScheduleId: { type: Schema.Types.ObjectId, ref: 'Schedule' },
  scheduleSettings: {
    autoGenerateFromSchedule: { type: Boolean, default: true },
    generateDaysAhead: { type: Number, default: 30, min: 1, max: 365 },
    allowScheduleOverrides: { type: Boolean, default: true }
  },



  // Display settings
  displaySettings: {
    showSchedulePattern: { type: Boolean, default: true },
    showAvailabilityOnly: { type: Boolean, default: false },
    groupSimilarSlots: { type: Boolean, default: true }
  },

  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
calendarSchema.index({ ownerId: 1, ownerType: 1 });
calendarSchema.index({ ownerId: 1, ownerType: 1, isActive: 1 });
calendarSchema.index({ isShared: 1, isActive: 1 });
calendarSchema.index({ defaultScheduleId: 1 });
calendarSchema.index({ hasSchedule: 1, isActive: 1 });
calendarSchema.index({ ownerType: 1, isActive: 1 });

calendarSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods

// Schedule-related methods
calendarSchema.methods.hasActiveSchedule = function(): boolean {
  return this.hasSchedule && this.isActive && !!this.defaultScheduleId;
};





calendarSchema.methods.allowsScheduleOverrides = function(): boolean {
  return this.scheduleSettings?.allowScheduleOverrides !== false;
};

calendarSchema.methods.getDisplayMode = function(): 'schedule' | 'hybrid' {
  if (!this.hasSchedule) return 'schedule';
  if (this.displaySettings?.showSchedulePattern) return 'hybrid';
  return 'schedule';
};

export const Calendar = model<ICalendar>('Calendar', calendarSchema);
