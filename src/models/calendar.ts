import { Schema, model, Document, Types } from 'mongoose';

export interface ICalendar extends Document {
  tutorId: Types.ObjectId; // Must be a tutor
  name: string;
  description?: string;
  color?: string;
  isShared?: boolean; // whether this calendar is visible to learners or public
  timezone?: string; // Tutor's timezone for proper scheduling
  isActive?: boolean; // Whether the calendar is currently active

  // Schedule integration
  hasSchedule?: boolean; // Whether this calendar uses schedule-based availability
  defaultScheduleId?: Types.ObjectId; // Reference to default schedule
  scheduleSettings?: {
    autoGenerateFromSchedule?: boolean; // Auto-generate events from schedule
    generateDaysAhead?: number; // How many days ahead to generate
    allowScheduleOverrides?: boolean; // Allow manual event overrides
  };

  bookingSettings?: {
    autoAcceptBookings?: boolean;
    advanceBookingDays?: number; // How many days in advance can students book
    minBookingNotice?: number; // Minimum hours notice required for booking
    maxBookingsPerDay?: number; // Maximum bookings allowed per day
    allowRecurringBookings?: boolean; // Allow students to book recurring sessions
    requireApprovalForRecurring?: boolean; // Require approval for recurring bookings
  };

  // Display settings
  displaySettings?: {
    showSchedulePattern?: boolean; // Show underlying schedule pattern in calendar view
    showAvailabilityOnly?: boolean; // Only show available slots to students
    groupSimilarSlots?: boolean; // Group similar time slots for cleaner display
  };

  createdAt: Date;
  updatedAt: Date;
}

const calendarSchema = new Schema<ICalendar>({
  tutorId: { type: Schema.Types.ObjectId, ref: 'Tutor', required: true },
  name: { type: String, required: true },
  description: String,
  color: { type: String, default: '#3B82F6' }, // Blue color
  isShared: { type: Boolean, default: true }, // Tutors want learners to see their calendar by default
  timezone: { type: String, default: 'UTC' },
  isActive: { type: Boolean, default: true },

  // Schedule integration
  hasSchedule: { type: Boolean, default: false },
  defaultScheduleId: { type: Schema.Types.ObjectId, ref: 'Schedule' },
  scheduleSettings: {
    autoGenerateFromSchedule: { type: Boolean, default: true },
    generateDaysAhead: { type: Number, default: 30, min: 1, max: 365 },
    allowScheduleOverrides: { type: Boolean, default: true }
  },

  bookingSettings: {
    autoAcceptBookings: { type: Boolean, default: false },
    advanceBookingDays: { type: Number, default: 30 },
    minBookingNotice: { type: Number, default: 24 }, // 24 hours
    maxBookingsPerDay: { type: Number, default: 8 },
    allowRecurringBookings: { type: Boolean, default: true },
    requireApprovalForRecurring: { type: Boolean, default: true }
  },

  // Display settings
  displaySettings: {
    showSchedulePattern: { type: Boolean, default: true },
    showAvailabilityOnly: { type: Boolean, default: false },
    groupSimilarSlots: { type: Boolean, default: true }
  },

  createdAt: { type: Date, default: () => new Date() },
  updatedAt: { type: Date, default: () => new Date() }
});

// Add indexes for better query performance
calendarSchema.index({ tutorId: 1 });
calendarSchema.index({ tutorId: 1, isActive: 1 });
calendarSchema.index({ isShared: 1, isActive: 1 });
calendarSchema.index({ defaultScheduleId: 1 });
calendarSchema.index({ hasSchedule: 1, isActive: 1 });

calendarSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
calendarSchema.methods.canAcceptBookings = function(): boolean {
  return this.isActive && this.isShared;
};

calendarSchema.methods.getBookingWindow = function(): { start: Date, end: Date } {
  const now = new Date();
  const start = new Date(now.getTime() + (this.bookingSettings?.minBookingNotice || 24) * 60 * 60 * 1000);
  const end = new Date(now.getTime() + (this.bookingSettings?.advanceBookingDays || 30) * 24 * 60 * 60 * 1000);
  return { start, end };
};

// Schedule-related methods
calendarSchema.methods.hasActiveSchedule = function(): boolean {
  return this.hasSchedule && this.isActive && !!this.defaultScheduleId;
};

calendarSchema.methods.shouldAutoGenerateEvents = function(): boolean {
  return this.hasActiveSchedule() &&
         this.scheduleSettings?.autoGenerateFromSchedule !== false;
};

calendarSchema.methods.getGenerationWindow = function(): { start: Date, end: Date } {
  const now = new Date();
  const daysAhead = this.scheduleSettings?.generateDaysAhead || 30;
  const start = new Date(now);
  start.setHours(0, 0, 0, 0); // Start of today
  const end = new Date(now.getTime() + daysAhead * 24 * 60 * 60 * 1000);
  end.setHours(23, 59, 59, 999); // End of last day
  return { start, end };
};

calendarSchema.methods.allowsScheduleOverrides = function(): boolean {
  return this.scheduleSettings?.allowScheduleOverrides !== false;
};

calendarSchema.methods.getDisplayMode = function(): 'schedule' | 'events' | 'hybrid' {
  if (!this.hasSchedule) return 'events';
  if (this.displaySettings?.showSchedulePattern) return 'hybrid';
  return 'events';
};

export const Calendar = model<ICalendar>('Calendar', calendarSchema);
