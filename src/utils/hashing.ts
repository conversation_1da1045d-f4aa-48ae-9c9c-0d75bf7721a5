import jwt, { JwtPayload } from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { JwtSignPayload } from "../middlewares/auth";
// import {
//   RtcTokenBuilder,
//   RtcRole,
//   RtmRole,
//   RtmTokenBuilder,
// } from "agora-access-token";
import { ChatTokenBuilder, RtcTokenBuilder, RtcRole } from "agora-token";

const appID = process.env.AGORA_APP_ID!;
const appCertificate = process.env.AGORA_APP_CERTIFICATE!;

export const generateToken = (
  payload: JwtSignPayload,
  opts?: Partial<JwtPayload> & Partial<{ rememberMe?: boolean }>
) => {
  const { rememberMe, ...jwtOpts } = opts || {};

  return jwt.sign(payload, process.env.JWT_SECRET!, {
    ...jwtOpts,
    expiresIn: jwtOpts?.expiresIn || (rememberMe ? "30d" : "5h"),
  });
};

export const hashPassword = async (value: string) =>
  await bcrypt.hash(value, 12);

export const comparePassword = async (value: string, hash: string) =>
  await bcrypt.compare(value, hash);

// userID Set to 0 for a dynamic UID
export const generateRTCToken = (
  channelName: string,
  userId: string | number = 0
) => {
  const role = RtcRole.PUBLISHER;

  const expirationTimeInSeconds = 86400; // Token valid for 1 day

  const currentTimestamp = Math.floor(Date.now() / 1000);

  const privilegeExpire = currentTimestamp + expirationTimeInSeconds;
  const tokenExpire = privilegeExpire;

  return RtcTokenBuilder.buildTokenWithUid(
    appID,
    appCertificate,
    channelName,
    Number(userId) || 0,
    role,
    tokenExpire,
    privilegeExpire
  );
};

export const generateAgoraChatToken = (expire = 86400) => {
  return ChatTokenBuilder.buildAppToken(
    process.env.AGORA_APP_ID!,
    process.env.AGORA_APP_CERTIFICATE!,
    expire
  );
};

export const generateAgoraChatUserToken = (userId: string, expire = 86400) => {
  return ChatTokenBuilder.buildUserToken(
    process.env.AGORA_APP_ID!,
    process.env.AGORA_APP_CERTIFICATE!,
    userId,
    expire
  );
};
