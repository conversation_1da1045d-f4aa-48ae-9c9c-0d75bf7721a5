import { JsonResponseProps, KeyValuePair } from "../types/misc";
import { Response } from "express";

type JSONResDataProps =
  | {
      message?: string;
      data: JsonResponseProps["data"];
      success?: boolean;
      details?: KeyValuePair;
    }
  | string;

export const getJsonResponse = (data: JSONResDataProps, code = "OK") => {
  const response: JsonResponseProps = {
    success: true,
    status: 200,
    code,
    message: "OK",
    data: null,
    details: null,
  };

  if (typeof data === "string") {
    response.message = data;
    response.data = null;
  } else {
    response.message = data.message || "OK";
    response.data = data.data || null;

    if (typeof data.success === "boolean") response.success = data.success;

    if (data.details && Object.keys(data.details).length)
      response.details = data.details;
  }

  return response;
};

export const createOkResponse = (
  res: Response,
  data: JSONResDataProps,
  code?: string
) => {
  return res.status(200).json(getJsonResponse(data, code));
};

export const capitalize = (str: string) => {
  if (!str) return "";

  return str.charAt(0).toUpperCase() + str.slice(1);
};
