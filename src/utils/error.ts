import fs from "fs";
import path from "path";

type Action = "post" | "get" | "put" | "delete" | "mail" | "others";

interface ErrorLogEntry {
  timestamp: string;
  message: string;
  metadata?: Record<string, any>;
}

interface LogFileStructure {
  post: ErrorLogEntry[];
  get: ErrorLogEntry[];
  put: ErrorLogEntry[];
  delete: ErrorLogEntry[];
  mail: ErrorLogEntry[];
  others: ErrorLogEntry[];
}

const dir = path.join(process.cwd(), "src/logs");

const LOG_FILE_PATH = path.join(dir, "/error-log.json");

export const logError = (
  error: string,
  action: Action = "others",
  metadata: Record<string, any> = {}
): void => {
  try {
    const timestamp = new Date().toISOString();
    const logEntry: ErrorLogEntry = {
      timestamp,
      message: error,
      metadata,
    };

    let logs: LogFileStructure = {
      post: [],
      get: [],
      put: [],
      delete: [],
      mail: [],
      others: [],
    };

    if (fs.existsSync(LOG_FILE_PATH)) {
      const fileContent = fs.readFileSync(LOG_FILE_PATH, "utf8");

      try {
        logs = { ...logs, ...(fileContent ? JSON.parse(fileContent) : {}) };
      } catch (e: any) {
        console.log("⚠️ Error parsing existing log file. Starting fresh.");
      }
    } else fs.mkdirSync(dir, { recursive: true });

    if (!logs[action]) logs[action] = [];

    logs[action].push(logEntry);

    fs.writeFileSync(LOG_FILE_PATH, JSON.stringify(logs), "utf8");
  } catch (err) {
    console.log("❌ Failed to write to error log file:", err);
  }
};
