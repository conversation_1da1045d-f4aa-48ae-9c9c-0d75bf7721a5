import { Types } from 'mongoose';
import { Schedule, ISchedule, IDaySchedule, ITimeSlot } from '../models/Schedule';
import { Event } from '../models/Event';

export interface GenerationOptions {
  startDate?: Date;
  endDate?: Date;
  overwriteExisting?: boolean;
  dryRun?: boolean;
  skipConflictCheck?: boolean;
}

export interface GenerationResult {
  success: boolean;
  eventsCreated: number;
  eventsSkipped: number;
  conflicts: Array<{
    date: Date;
    timeSlot: string;
    reason: string;
  }>;
  errors: string[];
}

/**
 * Generate events from a schedule pattern
 */
export const generateEventsFromSchedule = async (
  scheduleId: Types.ObjectId,
  options: GenerationOptions = {}
): Promise<GenerationResult> => {
  try {
    const schedule = await Schedule.findById(scheduleId).populate('calendarId');
    if (!schedule) {
      return {
        success: false,
        eventsCreated: 0,
        eventsSkipped: 0,
        conflicts: [],
        errors: ['Schedule not found']
      };
    }

    const calendar = schedule.calendarId as any;
    if (!calendar || !calendar.isActive) {
      return {
        success: false,
        eventsCreated: 0,
        eventsSkipped: 0,
        conflicts: [],
        errors: ['Calendar not found or inactive']
      };
    }

    // Determine date range
    const now = new Date();
    const startDate = options.startDate || now;
    const endDate = options.endDate || new Date(now.getTime() + (schedule.settings.advanceGenerationDays || 30) * 24 * 60 * 60 * 1000);

    const result: GenerationResult = {
      success: true,
      eventsCreated: 0,
      eventsSkipped: 0,
      conflicts: [],
      errors: []
    };

    // Get existing events in the date range if not overwriting
    let existingEvents: any[] = [];
    if (!options.overwriteExisting) {
      existingEvents = await Event.find({
        calendarId: schedule.calendarId,
        startDateTime: { $gte: startDate, $lte: endDate }
      });
    }

    const currentDate = new Date(startDate);
    currentDate.setHours(0, 0, 0, 0);

    while (currentDate <= endDate) {
      try {
        const dayResult = await generateEventsForDay(schedule, currentDate, existingEvents, options);
        result.eventsCreated += dayResult.eventsCreated;
        result.eventsSkipped += dayResult.eventsSkipped;
        result.conflicts.push(...dayResult.conflicts);
        result.errors.push(...dayResult.errors);
      } catch (error) {
        result.errors.push(`Error generating events for ${currentDate.toDateString()}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Update last generation timestamp
    if (!options.dryRun && result.eventsCreated > 0) {
      await Schedule.findByIdAndUpdate(scheduleId, {
        lastEventGeneration: new Date()
      });
    }

    return result;

  } catch (error) {
    return {
      success: false,
      eventsCreated: 0,
      eventsSkipped: 0,
      conflicts: [],
      errors: [error instanceof Error ? error.message : 'Unknown error occurred']
    };
  }
};

/**
 * Generate events for a specific day
 */
const generateEventsForDay = async (
  schedule: ISchedule,
  date: Date,
  existingEvents: any[],
  options: GenerationOptions
): Promise<GenerationResult> => {
  const result: GenerationResult = {
    success: true,
    eventsCreated: 0,
    eventsSkipped: 0,
    conflicts: [],
    errors: []
  };

  // Get schedule for this day
  const daySchedule = schedule.getScheduleForDate(date);
  if (!daySchedule || !daySchedule.isWorkingDay) {
    return result; // No work scheduled for this day
  }

  // Process each time slot
  for (const timeSlot of daySchedule.timeSlots) {
    if (!timeSlot.isAvailable || timeSlot.sessionType === 'break') {
      continue;
    }

    try {
      const eventResult = await generateEventForTimeSlot(
        schedule,
        date,
        timeSlot,
        existingEvents,
        options
      );

      if (eventResult.created) {
        result.eventsCreated++;
      } else if (eventResult.skipped) {
        result.eventsSkipped++;
        if (eventResult.conflict) {
          result.conflicts.push(eventResult.conflict);
        }
      }

      if (eventResult.error) {
        result.errors.push(eventResult.error);
      }

    } catch (error) {
      result.errors.push(`Error processing time slot ${timeSlot.startTime}-${timeSlot.endTime}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return result;
};

/**
 * Generate event for a specific time slot
 */
const generateEventForTimeSlot = async (
  schedule: ISchedule,
  date: Date,
  timeSlot: ITimeSlot,
  existingEvents: any[],
  options: GenerationOptions
): Promise<{
  created: boolean;
  skipped: boolean;
  conflict?: { date: Date; timeSlot: string; reason: string };
  error?: string;
}> => {
  try {
    // Create start and end datetime
    const [startHour, startMinute] = timeSlot.startTime.split(':').map(Number);
    const [endHour, endMinute] = timeSlot.endTime.split(':').map(Number);

    const startDateTime = new Date(date);
    startDateTime.setHours(startHour, startMinute, 0, 0);

    const endDateTime = new Date(date);
    endDateTime.setHours(endHour, endMinute, 0, 0);

    // Check for existing events at this time
    if (!options.overwriteExisting) {
      const hasConflict = existingEvents.some(event => {
        return (
          (startDateTime >= event.startDateTime && startDateTime < event.endDateTime) ||
          (endDateTime > event.startDateTime && endDateTime <= event.endDateTime) ||
          (startDateTime <= event.startDateTime && endDateTime >= event.endDateTime)
        );
      });

      if (hasConflict) {
        return {
          created: false,
          skipped: true,
          conflict: {
            date,
            timeSlot: `${timeSlot.startTime}-${timeSlot.endTime}`,
            reason: 'Existing event conflicts'
          }
        };
      }
    }

    // Skip if this is a dry run
    if (options.dryRun) {
      return { created: true, skipped: false };
    }

    // Create the event
    const event = new Event({
      calendarId: schedule.calendarId,
      tutorId: schedule.tutorId,
      title: `Available - ${timeSlot.sessionType || 'Lesson'}`,
      description: timeSlot.notes || `Generated from schedule: ${schedule.name}`,
      startDateTime,
      endDateTime,
      status: 'available',
      visibility: 'public',
      priority: 3,
      bookingInfo: {
        maxStudents: timeSlot.maxStudents || 1,
        currentBookings: 0,
        requiresApproval: false,
        price: timeSlot.price
      },
      metadata: {
        generatedFromSchedule: true,
        scheduleId: schedule._id,
        originalTimeSlot: timeSlot
      }
    });

    await event.save();

    return { created: true, skipped: false };

  } catch (error) {
    return {
      created: false,
      skipped: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Check for schedule conflicts
 */
export const checkScheduleConflicts = async (
  scheduleId: Types.ObjectId,
  startDate: Date,
  endDate: Date
): Promise<Array<{
  date: Date;
  conflicts: Array<{
    timeSlot: string;
    existingEvent: {
      id: string;
      title: string;
      startDateTime: Date;
      endDateTime: Date;
    };
  }>;
}>> => {
  try {
    const schedule = await Schedule.findById(scheduleId);
    if (!schedule) {
      throw new Error('Schedule not found');
    }

    const conflicts = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const daySchedule = schedule.getScheduleForDate(currentDate);
      if (daySchedule && daySchedule.isWorkingDay) {
        const dayConflicts = await checkDayConflicts(schedule, currentDate, daySchedule);
        if (dayConflicts.conflicts.length > 0) {
          conflicts.push(dayConflicts);
        }
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return conflicts;

  } catch (error) {
    console.error('Error checking schedule conflicts:', error);
    return [];
  }
};

/**
 * Check conflicts for a specific day
 */
const checkDayConflicts = async (
  schedule: ISchedule,
  date: Date,
  daySchedule: IDaySchedule
): Promise<{
  date: Date;
  conflicts: Array<{
    timeSlot: string;
    existingEvent: {
      id: string;
      title: string;
      startDateTime: Date;
      endDateTime: Date;
    };
  }>;
}> => {
  const conflicts = [];

  // Get existing events for this day
  const existingEvents = await Event.find({
    calendarId: schedule.calendarId,
    startDateTime: {
      $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
      $lt: new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1)
    }
  });

  // Check each time slot against existing events
  for (const timeSlot of daySchedule.timeSlots) {
    if (!timeSlot.isAvailable || timeSlot.sessionType === 'break') {
      continue;
    }

    const [startHour, startMinute] = timeSlot.startTime.split(':').map(Number);
    const [endHour, endMinute] = timeSlot.endTime.split(':').map(Number);

    const slotStart = new Date(date);
    slotStart.setHours(startHour, startMinute, 0, 0);

    const slotEnd = new Date(date);
    slotEnd.setHours(endHour, endMinute, 0, 0);

    // Find conflicting events
    const conflictingEvents = existingEvents.filter(event => {
      return (
        (slotStart >= event.startDateTime && slotStart < event.endDateTime) ||
        (slotEnd > event.startDateTime && slotEnd <= event.endDateTime) ||
        (slotStart <= event.startDateTime && slotEnd >= event.endDateTime)
      );
    });

    for (const event of conflictingEvents) {
      conflicts.push({
        timeSlot: `${timeSlot.startTime}-${timeSlot.endTime}`,
        existingEvent: {
          id: (event._id as any).toString(),
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime
        }
      });
    }
  }

  return { date, conflicts };
};

/**
 * Clean up old generated events
 */
export const cleanupOldGeneratedEvents = async (
  calendarId: Types.ObjectId,
  olderThanDays: number = 7
): Promise<{ deletedCount: number }> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await Event.deleteMany({
      calendarId,
      endDateTime: { $lt: cutoffDate },
      'metadata.generatedFromSchedule': true,
      status: 'available' // Only delete unbooked generated events
    });

    return { deletedCount: result.deletedCount || 0 };

  } catch (error) {
    console.error('Error cleaning up old generated events:', error);
    return { deletedCount: 0 };
  }
};
