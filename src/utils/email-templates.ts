import { capitalize } from "./misc";
import { USER_PROFILE_TYPE } from "./profile";

export const studentWelcomeTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: " Welcome to Convolly – Let’s find you students!",
  html: `
      <!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Welcome to Convolly</title>
  </head>
  <body style="margin:0; padding:0;">
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
      <tr>
        <td align="center">
          <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
            <tr>
              <td>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Hi ${user.firstname},
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Welcome to <strong>Convolly</strong>, where expert tutors and curious minds meet.
                  We’re excited to help you reach your learning goals.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Here’s how to get started:
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>1. Find a Tutor:</strong> Explore our verified tutor profiles and filter by subject, availability, and reviews.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>2. Book a Trial Lesson:</strong> Try a free or discounted session to see if it’s the right fit.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  <strong>3. Need Help?</strong> Visit our Help Center or reach out to <a href="mailto:<EMAIL>" style="color:#1a0dab;"><EMAIL></a>.
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  Let’s make learning easy and enjoyable. 🚀
                </p>
                <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                  — The Convolly Team
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>

      `,
});

export const tutorWelcomeTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "Welcome to Convolly – Let’s find you students!",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Welcome to Convolly</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi ${user.firstname},
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You’re officially a Convolly tutor – congrats! 🎉
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Here’s how to get started:
                    </p>
                    <ul style="font-family:Arial, sans-serif; font-size:16px; color:#333333; padding-left:20px;">
                      <li>Complete Your Profile: Add teaching experience, intro video, and availability.</li>
                      <li>Respond Quickly: First impressions matter. Respond fast to student messages.</li>
                      <li>Offer a Trial: Help students get to know you.</li>
                    </ul>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Let’s make learning rewarding—for both you and your students.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const lessonConfirmationTemplate = (
  studentName: string,
  tutorName: string
) => ({
  subject: "Your lesson is confirmed ✅",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Your lesson is confirmed</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi <strong>${studentName}</strong>,
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your session with <strong>${tutorName}</strong> is confirmed for:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      📅 <strong>[Date & Time]</strong><br />
                      📍 Online via Convolly (Join link below)
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🔗 <a href="[Join Lesson]" style="color:#1a0dab;">Join Lesson</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      We recommend arriving 5 minutes early. Looking forward to your session!
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const guideToFindTutorTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "Your Guide to Finding the Perfect Tutor",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Your Guide to Finding the Perfect Tutor</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi ${user.firstname},
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Here are 3 simple tips to get the most out of <strong>Convolly</strong>:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>1. Be clear about your goals –</strong> Whether it's exams, fluency, or career, the right tutor can tailor your lessons.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>2. Use filters wisely –</strong> Sort by availability, native language, or specialties.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      <strong>3. Start with a trial –</strong> A trial session helps build trust and understanding.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Book your first lesson today and start learning smarter.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const studentLessonReminderTemplate = () => ({
  subject: "Reminder: Your lesson is in 24 hours / 1 hour",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Lesson Reminder</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi [First Name],
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your lesson with <strong>[Tutor Name]</strong> is coming up:
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🗓️ <strong>[Date & Time]</strong><br />
                      ⏰ <strong>[Countdown: 24 hrs / 1 hr left]</strong>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🔗 <a href="[Join Lesson]" style="color:#1a0dab;">Join Lesson</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Don't forget to bring any materials or questions you have.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const studentLessonStatusTemplate = () => ({
  subject: "Your lesson has been [Rescheduled/Cancelled]",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Lesson Update</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi [First Name],
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your upcoming lesson with <strong>[Tutor Name]</strong> has been <strong>[rescheduled/cancelled]</strong>.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      📅 <strong>New Date (if rescheduled): [New Date & Time]</strong>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Visit your dashboard to manage your lessons or find another tutor.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const paymentSuccessTemplate = (
  user: USER_PROFILE_TYPE,
  amount: number
) => ({
  subject: "Payment Successful – Thank you!",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Payment Confirmation</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi [First Name],
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your payment of <strong>€[Amount]</strong> was successful. 🎉
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You can now schedule your lessons on Convolly.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🧾 <a href="[View Receipt]" style="color:#1a0dab;">View Receipt</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const subscriptionPausedTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "Your subscription is now [Paused/Cancelled]",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Subscription Update</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi [First Name],
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      We’ve confirmed your request to <strong>[pause/cancel]</strong> your subscription.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      You can always return when you're ready to continue your learning journey.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Need help? Contact us at <a href="mailto:<EMAIL>" style="color:#1a0dab;"><EMAIL></a>.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const subscriptionReminderTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "Your subscription is ending soon",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Subscription Expiring Reminder</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Hi [First Name],
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      Your subscription will expire on <strong>[Date]</strong>. Renew now to avoid interruptions in your lessons.
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      🛒 <a href="[Renew Now]" style="color:#1a0dab;">Renew Now</a>
                    </p>
                    <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                      — The Convolly Team
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const lowLessonBalanceTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "You have 1 lesson left! ⏳",
  html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8" />
          <title>Low Lesson Balance Alert</title>
        </head>
        <body style="margin:0; padding:0;">
          <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
            <tr>
              <td align="center">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                  <tr>
                    <td>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        Hi [First Name],
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        You're down to your last lesson. Don’t miss out – top up now to continue seamlessly.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        ➕ <a href="[Buy More Lessons]" style="color:#1a0dab;">Buy More Lessons</a>
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        — The Convolly Team
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
      </html>
    `,
});

export const inActiveStudentTemplate = (user: USER_PROFILE_TYPE) => ({
  subject: "We miss you! Ready to book your next lesson?",
  html: `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8" />
          <title>Inactive User Reminder</title>
        </head>
        <body style="margin:0; padding:0;">
          <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
            <tr>
              <td align="center">
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                  <tr>
                    <td>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        Hi [First Name],
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        It’s been a while! Your progress matters, and your tutor is waiting.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        🏁 Pick up where you left off and reach your goals.
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        📚 <a href="[Book a Lesson]" style="color:#1a0dab;">Book a Lesson</a>
                      </p>
                      <p style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                        — The Convolly Team
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
      </html>
    `,
});

export const tutorApprovalStatusTemplate = (status: string) => ({
  subject: `Your profile has been ${capitalize(status)}`,
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Profile Status Update</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>Hi [Tutor Name],</p>
                    <p>Your tutor profile has been <strong>${status}</strong>.</p>
                    <!-- If Approved -->
                    <p>[If Approved]: Start accepting bookings today!</p>
                    <!-- If Rejected -->
                    <p>[If Rejected]: Here’s what needs fixing:</p>
                    <p><em>[Feedback]</em></p>
                    <p>
                      <a href="[Update Profile]" style="color:#1a0dab;">Update Profile</a>
                    </p>
                    <p>— The Convolly Team</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const lessonBookingTemlate = () => ({
  subject: "You have a new lesson booking!",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>New Lesson Booking Notification</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>Hi [Tutor Name],</p>
                    <p>
                      <strong>[Student Name]</strong> has booked a lesson with you: 🗓️ <strong>[Date & Time]</strong>
                    </p>
                    <p>⏰ Check your calendar and be ready to teach.</p>
                    <p>
                      🔗 <a href="[View Booking]" style="color:#1a0dab;">View Booking</a>
                    </p>
                    <p>— The Convolly Team</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const tutorLessonReminder = () => ({
  subject: "You have a new lesson booking!",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>New Lesson Booking Notification</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>Hi [Tutor Name],</p>
                    <p>
                      <strong>[Student Name]</strong> has booked a lesson with you: 🗓️ <strong>[Date & Time]</strong>
                    </p>
                    <p>⏰ Check your calendar and be ready to teach.</p>
                    <p>
                      🔗 <a href="[View Booking]" style="color:#1a0dab;">View Booking</a>
                    </p>
                    <p>— The Convolly Team</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const lessonCancelledByStudent = () => ({
  subject: "Lesson update: [Cancelled/Rescheduled] by student",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Lesson Update</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>Hi [Tutor Name],</p>
                    <p><strong>[Student Name]</strong> has <strong>[cancelled/rescheduled]</strong> your upcoming lesson.</p>
                    <p>📆 New Date (if applicable): <strong>[Date & Time]</strong></p>
                    <p>Check your calendar for more details.</p>
                    <p>— The Convolly Team</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const payoutConfirmationTemplate = (
  user: USER_PROFILE_TYPE,
  amount: number
) => ({
  subject: "You've been paid 💸",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Payout Confirmation</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>Hi [Tutor Name],</p>
                    <p>We’ve sent your payout for completed lessons.</p>
                    <p>💰 <strong>Amount:</strong> €[Amount]</p>
                    <p>📆 <strong>Payout Date:</strong> [Date]</p>
                    <p>💳 <strong>Method:</strong> [Bank/Wallet Name]</p>
                    <p>Thank you for being part of Convolly!</p>
                    <p>— The Convolly Team</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});

export const approveTutorTemplate = () => ({
  subject: "Tutor Profile Awaiting Approval",
  html: `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8" />
        <title>Tutor Approval Notification</title>
      </head>
      <body style="margin:0; padding:0;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color:#f9f9f9;">
          <tr>
            <td align="center">
              <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color:#ffffff; padding:20px;">
                <tr>
                  <td style="font-family:Arial, sans-serif; font-size:16px; color:#333333;">
                    <p>A new tutor has completed onboarding and is awaiting approval.</p>
                    <p><strong>Profile ID:</strong> \${userId}</p>
                    <p><strong>Profile Email:</strong> \${req.user.email}</p>
                    <p>Please review and approve the profile in the admin dashboard.</p>
                    <p>— Convolly System Notification</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  `,
});
