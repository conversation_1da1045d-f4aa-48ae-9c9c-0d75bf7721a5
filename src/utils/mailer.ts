import nodemailer from "nodemailer";
import { IS_PROD } from "../config/constants";

export type SendMailParams = {
  subject: string;
  html: string;
  from?: "no-reply" | "admin" | "marketing" | "support";
};

export const sendResetCode = async (
  user: { email: string; id: string },
  code: string,
  expireAt: Date
) => {
  await sendMail(user.email, {
    subject: "Reset your password",
    html: `
      <p>Your password reset code is:</p>
      <h2>${code}</h2>
      <p>This code will expire in ${expireAt.getMinutes()} minutes.</p>
    `,
  });
};

export const sendMail = async (to: string, params: SendMailParams) => {
  const aliasMap = {
    "no-reply": "<EMAIL>",
    admin: "<EMAIL>",
    marketing: "<EMAIL>",
    support: "<EMAIL>",
  };

  const fromEmail = aliasMap[params.from || "no-reply"];

  const transporter = nodemailer.createTransport({
    host: "smtp.zoho.com",
    port: 465,
    secure: true, // SSL
    auth: {
      user: fromEmail,
      pass: process.env.ZOHO_MAIL_APP_PASSWORD,
    },
  });

  await transporter.sendMail({
    to: to || IS_PROD ? undefined : aliasMap.support,
    from: fromEmail,
    subject: params.subject,
    html: params.html,
  });
};
