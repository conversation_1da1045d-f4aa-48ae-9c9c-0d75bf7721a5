import { Model } from "mongoose";
import Tu<PERSON>, { ITutor } from "../models/tutor";
import Student, { IStudent } from "../models/student";
import { IProfile } from "../models/profile";
import { KeyValuePair } from "../types/misc";
import Admin, { IAdmin } from "../models/admin";
import { getErrorResponse } from "../middlewares/errorHandler";
import { ERROR_INVALID_ROLE } from "../config/constants";
import { uploadAndDeleteFile, uploadAndDeleteFiles } from "./file";

export type USER_PROFILE_TYPE = IAdmin | ITutor | IStudent;

export const safeUserUpdateBody = (user: Partial<IProfile>, strict = true) => {
  const body: Partial<IProfile> = {
    firstname: user.firstname,
    lastname: user.lastname,
  };

  if (!strict) {
    body.email = user.email;
  }

  return body;
};

export function getProfileModelByRole(role: "student"): Model<IStudent>;
export function getProfileModelByRole(role: "tutor"): Model<ITutor>;
export function getProfileModelByRole(role: "admin"): Model<IAdmin>;

export function getProfileModelByRole(
  role: IProfile["role"]
): Model<IStudent> | Model<ITutor> | Model<IAdmin> {
  const modelMap = {
    student: Student,
    tutor: Tutor,
    admin: Admin,
  };

  const model = modelMap[role];

  if (!model) {
    throw getErrorResponse(ERROR_INVALID_ROLE);
  }

  return model;
}

export const getProfile = async (
  {
    email,
    id,
    role,
  }: {
    email?: string;
    id?: string;
    role?: IProfile["role"];
  } & KeyValuePair,
  opts?: { strict?: boolean; sort?: KeyValuePair }
): Promise<USER_PROFILE_TYPE | null> => {
  const query: KeyValuePair = {};

  if (email) query.email = email;
  else if (id) query._id = id;

  if (role === undefined) {
    const tutor = await Tutor.findOne(query);
    if (tutor) return tutor;
    else return await Student.findOne(query);
  } else {
    const model = getProfileModelByRole(role as any);

    return await model.findOne(query);
  }
};

export const getProfiles = async (
  query: KeyValuePair,
  opts?: { select?: string }
) => {
  let profiles: USER_PROFILE_TYPE[] = [];

  profiles = await Tutor.find(query).select(opts?.select || "");

  profiles = profiles.concat(
    await Student.find(query).select(opts?.select || "")
  );
};

export const updateProfileMedias = async (
  body: any,
  user: USER_PROFILE_TYPE | null,
  withError = false
) => {
  let errorDetails: any = null;

  const uploadSingle = async (key: string) => {
    if (body[key]) {
      try {
        body[key] = (
          await uploadAndDeleteFile(body[key], user?.[key as keyof typeof user])
        ).url;
      } catch (err: any) {
        if (!errorDetails) errorDetails = {};
        errorDetails[key] = {
          message: err.message || `Invalid upload URL for ${key}`,
          path: `${key}`,
        };
      }
    }
  };

  const handleUpdate = async (key: string) => {
    if (Array.isArray(body[key])) {
      const result = await uploadAndDeleteFiles(
        body[key],
        user![key as keyof typeof user]
      );

      body[key] = result.uploads;

      if (result.errors.length) {
        if (!errorDetails) errorDetails = {};
        errorDetails[key] = result.errors;
      }
    }
  };

  await uploadSingle("image");

  if (user) {
    switch (user.role) {
      case "tutor":
        await uploadSingle("introVideo");
        await handleUpdate("certificates");
        await handleUpdate("academics");
        break;
    }
  }

  if (withError && errorDetails)
    throw getErrorResponse({
      status: 400,
      details: errorDetails,
      message: "Invalid upload file",
    });

  return errorDetails;
};
