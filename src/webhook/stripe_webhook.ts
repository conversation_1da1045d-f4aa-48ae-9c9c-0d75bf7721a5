import { Request, Response } from 'express';
import Stripe from 'stripe';
import Subscription from '../models/subscription.model';
import SubscriptionService from '../services/subscriptionService';
import { getWebhookEventConfig, isValidEventType } from '../config/webhookConfig';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function handleStripeWebhook(req: Request, res: Response): Promise<void> {
  const sig = req.headers['stripe-signature'];

  // Check if signature exists
  if (!sig) {
    console.log('Webhook signature verification failed. No stripe-signature header value was provided.');
    res.status(400).send('Webhook Error: No stripe-signature header value was provided.');
    return;
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err: any) {
    console.log(`Webhook signature verification failed.`, err.message);
    res.status(400).send(`Webhook Error: ${err.message}`);
    return;
  }

  // Validate event type
  if (!isValidEventType(event.type)) {
    console.log(`Unsupported event type: ${event.type}`);
    res.json({ received: true, message: 'Event type not supported' });
    return;
  }

  const eventConfig = getWebhookEventConfig(event.type);
  console.log(`Processing webhook event: ${event.type} (${eventConfig?.description})`);

  // Handle the event
  switch (event.type) {
    case 'invoice.payment_succeeded':
      await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
      break;

    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
      break;

    case 'invoice.finalized':
      await handleInvoiceFinalized(event.data.object as Stripe.Invoice);
      break;

    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
      break;

    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
      break;

    case 'setup_intent.succeeded':
      await handleSetupIntentSucceeded(event.data.object as Stripe.SetupIntent);
      break;

    case 'payment_intent.succeeded':
      await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
  try {
    if (!(invoice as any).subscription) return;

    const stripeSubscriptionId = (invoice as any).subscription as string;
    const subscription = await SubscriptionService.findByStripeId(stripeSubscriptionId);

    if (!subscription) {
      console.log(`No subscription found for invoice ${invoice.id}`);
      return;
    }

    // Update subscription to active if it was pending
    if (subscription.status === 'pending_transfer' || subscription.status === 'incomplete') {
      // Update subscription status
      await SubscriptionService.updateSubscriptionStatus(
        subscription._id.toString(),
        {
          status: 'active',
          remainingLessons: subscription.lessonsPerWeek * 4
        },
        'webhook'
      );

      // Add payment to history
      await SubscriptionService.addPaymentToHistory(subscription._id.toString(), {
        amount: (invoice.amount_paid || 0) / 100,
        currency: invoice.currency,
        status: 'succeeded',
        stripePaymentIntentId: ((invoice as any).payment_intent as string) || '',
        stripeInvoiceId: invoice.id || '',
        description: `Payment for ${subscription.lessonsPerWeek} lessons weekly subscription`
      });

      // Notify about the update
      await SubscriptionService.notifySubscriptionUpdate(
        stripeSubscriptionId,
        'invoice.payment_succeeded',
        { invoiceId: invoice.id, amount: invoice.amount_paid }
      );

      console.log(`Subscription ${subscription._id} activated via webhook`);
    }
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
  try {
    if (!(invoice as any).subscription) return;

    const stripeSubscriptionId = (invoice as any).subscription as string;
    const subscription = await SubscriptionService.findByStripeId(stripeSubscriptionId);

    if (!subscription) return;

    // Add failed payment to history
    await SubscriptionService.addPaymentToHistory(subscription._id.toString(), {
      amount: (invoice.amount_due || 0) / 100,
      currency: invoice.currency,
      status: 'failed',
      stripePaymentIntentId: ((invoice as any).payment_intent as string) || '',
      stripeInvoiceId: invoice.id || '',
      description: `Failed payment for ${subscription.lessonsPerWeek} lessons weekly subscription`
    });

    // Notify about the failed payment
    await SubscriptionService.notifySubscriptionUpdate(
      stripeSubscriptionId,
      'invoice.payment_failed',
      { invoiceId: invoice.id, amount: invoice.amount_due }
    );

    console.log(`Payment failed recorded for subscription ${subscription._id}`);
  } catch (error) {
    console.error('Error handling invoice payment failed:', error);
  }
}

async function handleSubscriptionUpdated(stripeSubscription: Stripe.Subscription): Promise<void> {
  try {
    const updatedSubscription = await SubscriptionService.handleStripeStatusChange(stripeSubscription);

    if (updatedSubscription) {
      // Notify about the status change
      await SubscriptionService.notifySubscriptionUpdate(
        stripeSubscription.id,
        'customer.subscription.updated',
        {
          status: stripeSubscription.status,
          currentPeriodStart: (stripeSubscription as any).current_period_start,
          currentPeriodEnd: (stripeSubscription as any).current_period_end
        }
      );
    }
  } catch (error) {
    console.error('Error handling subscription updated:', error);
  }
}

async function handleSubscriptionDeleted(stripeSubscription: Stripe.Subscription): Promise<void> {
  try {
    const subscription = await SubscriptionService.findByStripeId(stripeSubscription.id);

    if (!subscription) return;

    await SubscriptionService.updateSubscriptionStatus(
      subscription._id.toString(),
      { status: 'cancelled' },
      'webhook'
    );

    // Notify about the cancellation
    await SubscriptionService.notifySubscriptionUpdate(
      stripeSubscription.id,
      'customer.subscription.deleted',
      { cancelledAt: new Date() }
    );

    console.log(`Subscription ${subscription._id} cancelled via webhook`);
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}

async function handleInvoiceFinalized(invoice: Stripe.Invoice): Promise<void> {
  try {
    if (!(invoice as any).subscription) return;

    const subscription = await Subscription.findOne({
      stripeSubscriptionId: (invoice as any).subscription as string
    });

    if (!subscription) {
      console.log(`No subscription found for finalized invoice ${invoice.id}`);
      return;
    }

    // If subscription is incomplete and invoice is finalized, it means payment is required
    if (subscription.status === 'incomplete' && invoice.status === 'open') {
      console.log(`Invoice ${invoice.id} finalized for incomplete subscription ${subscription._id}`);
      // The subscription will be handled when payment succeeds or fails
    }
  } catch (error) {
    console.error('Error handling invoice finalized:', error);
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  try {
    // Find subscription by payment intent
    let subscription = await Subscription.findOne({
      'paymentHistory.stripePaymentIntentId': paymentIntent.id
    });

    if (!subscription && (paymentIntent as any).invoice) {
      // Try to find by invoice
      const invoice = await stripe.invoices.retrieve((paymentIntent as any).invoice as string);
      if ((invoice as any).subscription) {
        const stripeSubscriptionId = (invoice as any).subscription as string;
        subscription = await SubscriptionService.findByStripeId(stripeSubscriptionId);

        if (subscription && subscription.status === 'incomplete') {
          // Activate the subscription
          await SubscriptionService.updateSubscriptionStatus(
            subscription._id.toString(),
            {
              status: 'active',
              remainingLessons: subscription.lessonsPerWeek * 4
            },
            'webhook'
          );

          // Add payment to history if not already there
          const existingPayment = subscription.paymentHistory?.find(
            p => p.stripePaymentIntentId === paymentIntent.id
          );

          if (!existingPayment) {
            await SubscriptionService.addPaymentToHistory(subscription._id.toString(), {
              amount: paymentIntent.amount / 100,
              currency: paymentIntent.currency,
              status: 'succeeded',
              stripePaymentIntentId: paymentIntent.id,
              stripeInvoiceId: ((paymentIntent as any).invoice as string) || '',
              description: `Payment for ${subscription.lessonsPerWeek} lessons weekly subscription`
            });
          }

          // Notify about the activation
          await SubscriptionService.notifySubscriptionUpdate(
            stripeSubscriptionId,
            'payment_intent.succeeded',
            { paymentIntentId: paymentIntent.id, amount: paymentIntent.amount }
          );

          console.log(`Subscription ${subscription._id} activated via payment intent succeeded`);
        }
      }
    }
  } catch (error) {
    console.error('Error handling payment intent succeeded:', error);
  }
}

async function handleSetupIntentSucceeded(setupIntent: Stripe.SetupIntent): Promise<void> {
  try {
    // This handles cases where setup intent was used instead of payment intent
    if (!setupIntent.metadata?.subscriptionId) return;

    const subscription = await Subscription.findOne({
      stripeSubscriptionId: setupIntent.metadata.subscriptionId
    });

    if (!subscription) return;

    // Check if the subscription is now active
    const stripeSubscription = await stripe.subscriptions.retrieve(setupIntent.metadata.subscriptionId);

    if (stripeSubscription.status === 'active' && subscription.status !== 'active') {
      subscription.status = 'active';
      subscription.remainingLessons = subscription.lessonsPerWeek * 4;
      await subscription.save();
      console.log(`Subscription ${subscription._id} activated after setup intent succeeded`);
    }
  } catch (error) {
    console.error('Error handling setup intent succeeded:', error);
  }
}