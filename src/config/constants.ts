import dotenv from "dotenv";
import { PROFILE_REGISTRATION_ROLES } from "../models/profile";

dotenv.config();

export const SERVER_ORIGIN = "https://convolly-backend.onrender.com";

export const CLIENT_ORIGIN = "";

export const IS_PROD = process.env.NODE_ENV === "production";

export const DEFAULT_AUTH_PROVIDER = "local";

export const DEFAULT_PROFILE_ROLE = "student";

export const ERROR_INVALID_ROLE = {
  message: "Invalid role specified",
  details: {
    allowedRoles: PROFILE_REGISTRATION_ROLES,
  },
  status: 403,
};

export const ERROR_FORBIDDEN_ACCESS = {
  message: "Forbidden Access",
  status: 403,
};

export const ERROR_INTERNAL_SERVER = {
  message: "Internal Server Error",
  status: 500,
};

export const AUTH_PROVIDERS = [
  DEFAULT_AUTH_PROVIDER,
  "apple",
  "google",
  "facebook",
  "linkedIn",
];
