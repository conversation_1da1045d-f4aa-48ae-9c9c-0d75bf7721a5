import { Server } from "socket.io";
import { CORS_CONFIG } from "./misc";
import http from "http";
import { Express } from "express";

const createServerSocket = (app: Express) => {
  const server = http.createServer(app);

  const io = new Server(server, {
    cors: CORS_CONFIG,
  });

  io.on("connection", (socket) => {
    socket.on("groupChatMessage", (data) => {
      socket.emit("groupChatMessage", data);
    });

    socket.on("broadcast", (data) => {
      socket.emit("broadcast", data);
    });

    socket.on("disconnect", () => {});
  });

  return server;
};

export default createServerSocket;
