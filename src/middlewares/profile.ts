import { NextFunction, Request, Response } from "express";
import { createErrorResponse } from "./errorHandler";
import { IProfile } from "../models/profile";
import { ERROR_FORBIDDEN_ACCESS } from "../config/constants";

export const isProfile =
  (role: IProfile["role"]) =>
  (req: Request, res: Response, next: NextFunction) => {
    try {

      if (req.user.role !== role || req.params.id !== req.user.id) {
        createErrorResponse(res, ERROR_FORBIDDEN_ACCESS);
        return;

      }

      next();
    } catch (err: any) {
      
      createErrorResponse(res, err, 500);
    }
  };

export const isTutor = isProfile("tutor");

export const isStudent = isProfile("student");
