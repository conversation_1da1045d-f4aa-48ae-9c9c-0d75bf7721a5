import { Response, NextFunction } from 'express';
import { Types } from 'mongoose';
import Subscription from '../models/subscription.model';
import Student from '../models/student';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

/**
 * Check if student is eligible for free trial
 */
const checkTrialEligibility = async (studentId: Types.ObjectId): Promise<{
  isEligible: boolean;
  hasUsedTrial: boolean;
  totalBookings: number;
}> => {
  try {
    // Check if student has used their free trial
    const student = await Student.findById(studentId);
    const hasUsedTrial = student?.hasUsedFreeTrial || false;

    return {
      isEligible: !hasUsedTrial,
      hasUsedTrial
    };
  } catch (error) {
    console.error('Error checking trial eligibility:', error);
    return { isEligible: false, hasUsedTrial: true };
  }
};

/**
 * Middleware to validate that a student has an active subscription with a specific tutor
 * OR is eligible for a 1-hour free trial
 * This middleware should be used before allowing booking operations
 */
export const validateSubscription = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Only students can make bookings
    if (req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can make bookings', 403);
      return;
    }

    // Get tutorId from request body or params
    const tutorId = req.body.tutorId || req.params.tutorId;

    if (!tutorId) {
      createErrorResponse(res, 'Tutor ID is required', 400);
      return;
    }

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Check for active subscription between student and tutor
    const activeSubscription = await Subscription.findOne({
      studentId: req.user._id,
      tutorId: tutorId,
      status: 'active'
    }).populate('tutorId', 'firstname lastname email');

    if (activeSubscription) {
      // Check if subscription allows lesson scheduling
      if (!activeSubscription.canScheduleLesson()) {
        createErrorResponse(res, 'Your subscription does not allow scheduling new lessons at this time', 403);
        return;
      }

      // Check if student has remaining lessons
      if (activeSubscription.remainingLessons <= 0) {
        createErrorResponse(res, 'You have no remaining lessons in your current subscription period', 403);
        return;
      }

      // Add subscription info to request for use in subsequent middleware/controllers
      req.subscription = activeSubscription;
      req.isTrialBooking = false;

      next();
      return;
    }

    // No active subscription found, check if eligible for free trial
    const trialEligibility = await checkTrialEligibility(req.user._id as Types.ObjectId);

    if (trialEligibility.isEligible) {
      // Student is eligible for free trial
      req.subscription = undefined;
      req.isTrialBooking = true;
      req.trialEligibility = trialEligibility;

      next();
      return;
    }

    // Not eligible for trial and no active subscription
    if (trialEligibility.hasUsedTrial) {
      createErrorResponse(res, 'You have already used your free trial. Please subscribe to book more sessions with this tutor.', 403);
    } else {
      createErrorResponse(res, 'You must have an active subscription with this tutor to book sessions', 403);
    }

  } catch (error) {
    console.error('Error validating subscription:', error);
    createErrorResponse(res, 'Failed to validate subscription', 500);
  }
};

/**
 * Middleware to validate subscription for specific booking operations
 * This is a more lenient check that allows viewing but restricts booking
 */
export const validateSubscriptionForViewing = async (
  req: AuthRequest, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Get tutorId from request body or params
    const tutorId = req.body.tutorId || req.params.tutorId;
    
    if (tutorId && Types.ObjectId.isValid(tutorId)) {
      // Check for any subscription (active, paused, etc.) between student and tutor
      const subscription = await Subscription.findOne({
        studentId: req.user._id,
        tutorId: tutorId,
        status: { $in: ['active', 'paused'] }
      }).populate('tutorId', 'firstname lastname email');

      // Add subscription info to request (can be null)
      req.subscription = subscription || undefined;
    }
    
    next();
  } catch (error) {
    console.error('Error validating subscription for viewing:', error);
    createErrorResponse(res, 'Failed to validate subscription', 500);
  }
};

/**
 * Check if a student can book with a specific tutor
 * Returns detailed information about subscription status and trial eligibility
 */
export const checkBookingEligibility = async (
  studentId: Types.ObjectId,
  tutorId: Types.ObjectId
): Promise<{
  canBook: boolean;
  subscription?: any;
  reason?: string;
  remainingLessons?: number;
  isTrialEligible?: boolean;
  trialInfo?: {
    hasUsedTrial: boolean;
  };
}> => {
  try {
    // Find active subscription
    const subscription = await Subscription.findOne({
      studentId,
      tutorId,
      status: 'active'
    }).populate('tutorId', 'firstname lastname email');

    if (subscription) {
      if (!subscription.canScheduleLesson()) {
        return {
          canBook: false,
          subscription,
          reason: 'Subscription does not allow scheduling lessons at this time'
        };
      }

      if (subscription.remainingLessons <= 0) {
        return {
          canBook: false,
          subscription,
          reason: 'No remaining lessons in current subscription period',
          remainingLessons: 0
        };
      }

      return {
        canBook: true,
        subscription,
        remainingLessons: subscription.remainingLessons,
        isTrialEligible: false
      };
    }

    // No active subscription, check trial eligibility
    const trialEligibility = await checkTrialEligibility(studentId);

    if (trialEligibility.isEligible) {
      return {
        canBook: true,
        subscription: null,
        remainingLessons: 0,
        isTrialEligible: true,
        trialInfo: {
          hasUsedTrial: trialEligibility.hasUsedTrial
        }
      };
    }

    // Not eligible for trial and no subscription
    let reason = 'No active subscription found with this tutor';
    if (trialEligibility.hasUsedTrial) {
      reason = 'Free trial already used. Active subscription required.';
    }

    return {
      canBook: false,
      reason,
      isTrialEligible: false,
      trialInfo: {
        hasUsedTrial: trialEligibility.hasUsedTrial
      }
    };
  } catch (error) {
    console.error('Error checking booking eligibility:', error);
    return {
      canBook: false,
      reason: 'Error checking subscription status'
    };
  }
};

/**
 * Middleware to check if user owns the resource (tutor checking their own calendar/events)
 */
export const validateTutorOwnership = async (
  req: AuthRequest, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    // Check if user is authenticated
    if (!req.user || !req.user._id) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    // Only tutors can manage calendars and events
    if (req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can manage calendars and events', 403);
      return;
    }

    // Add tutor info to request
    req.tutorId = req.user._id as Types.ObjectId;
    
    next();
  } catch (error) {
    console.error('Error validating tutor ownership:', error);
    createErrorResponse(res, 'Failed to validate tutor ownership', 500);
  }
};
