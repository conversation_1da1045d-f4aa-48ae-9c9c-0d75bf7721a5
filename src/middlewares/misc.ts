import { NextFunction, Request, Response } from "express";
import { createErrorResponse } from "./errorHandler";
import { KeyValuePair } from "../types/misc";

export const handle404 = (req: Request, res: Response) => {
  createErrorResponse(res, `Route ${req.originalUrl} not found.`, 404);
};

export const withRequestBody =
  (opts?: { errorDetails?: KeyValuePair }) =>
  (req: Request, res: Response, next: NextFunction) => {
    const { errorDetails } = opts || {};
    try {
      if (!req.body) {
        createErrorResponse(
          res,
          {
            message: "Request body is required",
            details: errorDetails,
          },
          400
        );
        return;
      }

      next();
    } catch (err: any) {
      createErrorResponse(res, err, 500);
    }
  };

export const isDevMode = (req: Request, res: Response, next: NextFunction) => {
  const envIsDev = process.env.NODE_ENV === "development";

  const host = req.hostname;
  const ip = req.ip || "";

  const isLocalhost =
    host === "localhost" ||
    host === "127.0.0.1" ||
    host === "::1" ||
    ip === "::1" ||
    ip === "127.0.0.1" ||
    ip.startsWith("::ffff:127.0.0.1");

  if (!envIsDev || !isLocalhost) {
    createErrorResponse(
      res,
      "This route is only available in development mode.",
      403
    );

    return;
  }
  next();
};
