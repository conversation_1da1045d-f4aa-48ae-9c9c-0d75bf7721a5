// Helper function to calculate subscription pricing
type PlanType = '1-lesson' | '2-lesson' | '3-lesson' | '5-lesson';

interface SubscriptionPlan {
  lessonsPerWeek: number;
  monthlyPrice: number;
}

export const getSubscriptionPricing = (planType: PlanType): SubscriptionPlan | undefined => {
  const pricing: Record<PlanType, SubscriptionPlan> = {
    '1-lesson': { lessonsPerWeek: 1, monthlyPrice: 12000 },
    '2-lesson': { lessonsPerWeek: 2, monthlyPrice: 22000 },
    '3-lesson': { lessonsPerWeek: 3, monthlyPrice: 31000 },
    '5-lesson': { lessonsPerWeek: 5, monthlyPrice: 48000 },
  };
  return pricing[planType];
};
