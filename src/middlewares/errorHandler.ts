import { Request, Response, NextFunction } from "express";
import { JsonResponseProps, KeyValuePair } from "../types/misc";
import { ERROR_INTERNAL_SERVER } from "../config/constants";

type ErrorResponseProps = Omit<JsonResponseProps, "data"> & {
  details?: any;
};

export type ErrorProps =
  | {
      statusCode?: any;
      details?: any;
      status?: any;
      message?: string;
      _status?: number;
    }
  | string;

export const getErrorResponse = (error: ErrorProps, status?: number) => {
  const err: ErrorResponseProps = {
    ...ERROR_INTERNAL_SERVER,
    success: false,
    status: status || 500,
    code: "INTERNAL_SERVER",
    details: null,
  };

  if (typeof error === "string") {
    if (error) err.message = error;
  } else {
    if (error.message) err.message = error.message;

    err.details = error.details;

    if (error._status) err.status = error._status;
    else if (!status) err.status = error.status || error.statusCode;
  }

  // log error in file
  if (err.status === 500)
    console.log(
      "🔴 Internal Server Error:",
      err.message,
      err.details,
      JSON.stringify((error as any).stack),
      err.code,
      new Date()
    );

  switch (err.status) {
    case 400:
      err.code = "BAD_REQUEST";
      break;
    case 401:
      err.code = "UNAUTHORIZED";
      break;
    case 403:
      err.code = "FORBIDDEN";
      break;
    case 404:
      err.code = "NOT_FOUND";
      break;
    case 409:
      err.code = "CONFLICT";
      break;
    case 422:
      err.code = "UNPROCESSABLE_ENTITY";
      break;
    case 429:
      err.code = "TOO_MANY_REQUESTS";
      break;
    default:
      err.code = "INTERNAL_SERVER";
      break;
  }

  return err;
};

export const createErrorResponse = (
  res: Response,
  error: ErrorProps,
  status = 400
) => {
  const _error = getErrorResponse(error, status);

  return res.status(_error.status).json(_error);
};

const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  createErrorResponse(res, err, err.status || 500);
};

export default errorHandler;
