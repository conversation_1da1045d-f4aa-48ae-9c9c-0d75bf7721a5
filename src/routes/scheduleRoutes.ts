import { Router } from 'express';
import {
  createSchedule,
  getTutorSchedules,
  getScheduleById,
  updateSchedule,
  deleteSchedule,
  addScheduleException,
  removeScheduleException,
  getScheduleAvailability
} from '../controllers/scheduleController';
import { isAuthenticated } from '../middlewares/auth';
import { validateTutorOwnership } from '../middlewares/subscriptionValidation';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// ============================================================================
// TUTOR ROUTES - Schedule Management
// ============================================================================

/**
 * POST /api/schedules
 * Create a new schedule for a tutor
 * Body: { calendarId, name, description, timezone, weeklySchedule, settings, isDefault }
 */
router.post('/',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  createSchedule
);

/**
 * GET /api/schedules/my
 * Get all schedules for the authenticated tutor
 * Query params: calendarId, includeInactive
 */
router.get('/my',
  isAuthenticated(),
  validateTutorOwnership,
  getTutorSchedules
);

/**
 * GET /api/schedules/:scheduleId
 * Get a specific schedule by ID
 */
router.get('/:scheduleId',
  isAuthenticated(),
  validateTutorOwnership,
  getScheduleById
);

/**
 * PUT /api/schedules/:scheduleId
 * Update a schedule
 * Body: { name, description, timezone, weeklySchedule, settings, isActive, isDefault, effectiveTo }
 */
router.put('/:scheduleId',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  updateSchedule
);

/**
 * DELETE /api/schedules/:scheduleId
 * Delete a schedule
 */
router.delete('/:scheduleId',
  isAuthenticated(),
  validateTutorOwnership,
  deleteSchedule
);

// ============================================================================
// SCHEDULE EXCEPTIONS MANAGEMENT
// ============================================================================

/**
 * POST /api/schedules/:scheduleId/exceptions
 * Add a schedule exception (holiday, vacation, etc.)
 * Body: { date, type, title, description, isRecurring, replacementSchedule }
 */
router.post('/:scheduleId/exceptions',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  addScheduleException
);

/**
 * DELETE /api/schedules/:scheduleId/exceptions
 * Remove a schedule exception
 * Body: { date }
 */
router.delete('/:scheduleId/exceptions',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  removeScheduleException
);

// ============================================================================
// PUBLIC/STUDENT ROUTES - View Schedule Availability
// ============================================================================

/**
 * GET /api/schedules/:scheduleId/availability
 * Get schedule availability for a date range
 * Query params: startDate, endDate, includeBooked
 * Public access for students to view tutor availability patterns
 */
router.get('/:scheduleId/availability',
  isAuthenticated(),
  getScheduleAvailability
);

export default router;
