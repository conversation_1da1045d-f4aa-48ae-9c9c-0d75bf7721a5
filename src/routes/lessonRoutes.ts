import { Router } from 'express';
import { 
  scheduleLesson, 
  getMyLessons, 
  getTutorSchedule 
} from '../controllers/lessonController';
import { isAuthenticated } from '../middlewares/auth';
import { validateSubscription } from '../middlewares/subscriptionValidation';

const router = Router();

// Student routes - schedule lessons with tutors
router.post('/schedule', isAuthenticated, validateSubscription, scheduleLesson);

// Get user's lessons (student or tutor)
router.get('/my-lessons', isAuthenticated, getMyLessons);

// Get tutor's schedule (for students to view available times)
router.get('/tutor/:tutorId/schedule', isAuthenticated, getTutorSchedule);

export default router;
