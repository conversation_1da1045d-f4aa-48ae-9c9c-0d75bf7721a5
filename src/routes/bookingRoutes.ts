import { Router } from 'express';
import {
  createBooking,
  getBookingsForTutor,
  updateBookingStatus,
  getStudentBookings,
  cancelBooking
} from '../controllers/bookingController';
import { isAuthenticated } from '../middlewares/auth';
import { validateSubscription, validateTutorOwnership } from '../middlewares/subscriptionValidation';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// Student booking routes
router.post('/',
  withRequestBody(),
  isAuthenticated(),
  validateSubscription,  // This checks subscription OR trial eligibility
  createBooking
);

router.get('/student',
  isAuthenticated(),
  getStudentBookings
);

router.put('/:id/cancel',
  withRequestBody(),
  isAuthenticated(),
  cancelBooking
);

// Tutor booking management routes
router.get('/tutor',
  isAuthenticated(),
  validateTutorOwnership,
  getBookingsForTutor
);

router.put('/:id/status',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  updateBookingStatus
);

export default router;
