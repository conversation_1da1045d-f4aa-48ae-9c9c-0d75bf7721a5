// routes/subscriptionRoutes.ts
import { Router } from 'express';
import { SubscriptionController } from '../controllers/SubscriptionController';
import { isAuthenticated } from '../middlewares/auth';
import { withRequestBody } from '../middlewares/misc';

const subscriptionRoutes = Router();
const subscriptionController = new SubscriptionController();

// Student subscription routes
subscriptionRoutes.get(
  '/students/:studentId/subscriptions',
  isAuthenticated(),
  subscriptionController.getStudentSubscriptions.bind(subscriptionController)
);

subscriptionRoutes.post(
  '/subscribe',
  withRequestBody(),
  isAuthenticated(),
  subscriptionController.createSubscription.bind(subscriptionController)
);

subscriptionRoutes.patch(
  '/:subscriptionId/pause',
  isAuthenticated(),
  subscriptionController.pauseSubscription.bind(subscriptionController)
);

subscriptionRoutes.patch(
  '/:subscriptionId/resume',
  isAuthenticated(),
  subscriptionController.resumeSubscription.bind(subscriptionController)
);

subscriptionRoutes.delete(
  '/:subscriptionId/cancel',
  isAuthenticated(),
  subscriptionController.cancelSubscription.bind(subscriptionController)
);

subscriptionRoutes.post(
  '/:subscriptionId/switch-tutor',
  withRequestBody(),
  isAuthenticated(),
  subscriptionController.switchTutor.bind(subscriptionController)
);

// Tutor subscription routes
subscriptionRoutes.get(
  '/tutor/subscriptions',
  isAuthenticated(),
  subscriptionController.getTutorSubscriptions.bind(subscriptionController)
);

// Admin subscription routes
subscriptionRoutes.get(
  '/admin/all',
  isAuthenticated(),
  subscriptionController.getAllSubscriptions.bind(subscriptionController)
);

subscriptionRoutes.patch(
  '/admin/:subscriptionId/approve-transfer',
  withRequestBody(),
  isAuthenticated(),
  subscriptionController.approveTutorTransfer.bind(subscriptionController)
);

// Payment confirmation routes
subscriptionRoutes.post(
  '/confirm-subscription',
  withRequestBody(),
  isAuthenticated(),
  subscriptionController.confirmSubscription.bind(subscriptionController)
);

subscriptionRoutes.post(
  '/:subscriptionId/confirm-payment',
  withRequestBody(),
  isAuthenticated(),
  subscriptionController.confirmPayment.bind(subscriptionController)
);

// Webhook notification route (internal use - no auth needed)
subscriptionRoutes.post(
  '/webhook-notify/:subscriptionId',
  withRequestBody(),
  subscriptionController.handleWebhookNotification.bind(subscriptionController)
);

// Get subscription by ID (should be last to avoid conflicts)
subscriptionRoutes.get(
  '/:subscriptionId',
  isAuthenticated(),
  subscriptionController.getSubscriptionById.bind(subscriptionController)
);

export default  subscriptionRoutes;