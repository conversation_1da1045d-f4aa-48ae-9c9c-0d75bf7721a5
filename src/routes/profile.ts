import { Router } from "express";
import tutorRouter from "./tutor";
import studentRouter from "./student";
import { withRequestBody } from "../middlewares/misc";
import { isAuthenticated } from "../middlewares/auth";
import { onBoardUser } from "../hooks/profile";
import adminRouter from "./adminRouter";

const profileRouter = Router();

profileRouter
  .route("/onboard")
  .put(withRequestBody(), isAuthenticated(), onBoardUser);

profileRouter
  .use("/students", studentRouter)
  .use("/tutors", tutorRouter)
  .use("/admin", adminRouter);

export default profileRouter;
