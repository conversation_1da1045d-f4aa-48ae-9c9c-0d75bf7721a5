import { Router } from "express";
import { withRequestBody } from "../middlewares/misc";
import {
  createLearner,
  getAllLearners,
  deleteLearner,
} from "../controllers/student";
import { getProfileById } from "../hooks/profile";
import Student from "../models/student";

const studentRouter = Router();

studentRouter
  .route("/")
  .post(withRequestBody(), createLearner)
  .get(getAllLearners);

studentRouter.route("/:id").get(getProfileById(Student)).delete(deleteLearner);

export default studentRouter;
