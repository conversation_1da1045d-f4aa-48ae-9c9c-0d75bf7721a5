import express from "express";
import { isAuthenticated, verifyJWT } from "../middlewares/auth";
import {
  createClassroom,
  createRTCToken,
  updateTimeLog,
} from "../controllers/classroom";
import { withRequestBody } from "../middlewares/misc";

const classRoomRouter = express.Router();

classRoomRouter.post(
  "/rtc",
  withRequestBody(),
  isAuthenticated({ strictVerification: false }),
  createRTCToken
);

classRoomRouter.patch(
  "/:sessionId/time-log",
  verifyJWT,
  withRequestBody(),
  updateTimeLog
);

classRoomRouter.post(
  "/create",
  withRequestBody(),
  isAuthenticated({ role: "tutor", withUserId: false }),
  createClassroom
);

export default classRoomRouter;
