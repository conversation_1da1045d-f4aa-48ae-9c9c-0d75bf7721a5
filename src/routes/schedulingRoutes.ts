import { Router } from 'express';
import {
  getAvailableTimeSlots,
  scheduleLesson,
  getMyScheduledLessons,
  rescheduleLesson,
  getTutorSchedule,
  getBookingDetails
} from '../controllers/schedulingController';
import { isAuthenticated } from '../middlewares/auth';
import { validateSubscription, validateSubscriptionForViewing, validateTutorOwnership } from '../middlewares/subscriptionValidation';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// ============================================================================
// PUBLIC/STUDENT ROUTES - View available time slots
// ============================================================================

/**
 * GET /api/scheduling/tutors/:tutorId/available-slots
 * Get available time slots for a specific tutor
 * Query params: startDate, endDate, duration, timezone
 */
router.get('/tutors/:tutorId/available-slots',
  isAuthenticated(),
  validateSubscriptionForViewing, // Adds subscription info but doesn't block access
  getAvailableTimeSlots
);

// ============================================================================
// STUDENT ROUTES - Schedule and manage lessons
// ============================================================================

/**
 * POST /api/scheduling/lessons
 * Schedule a new lesson
 * Requires active subscription OR trial eligibility
 */
router.post('/lessons',
  withRequestBody(),
  isAuthenticated(),
  validateSubscription, // This validates subscription OR trial eligibility
  scheduleLesson
);

/**
 * GET /api/scheduling/my-lessons
 * Get student's scheduled lessons
 * Query params: status, page, limit, upcoming, startDate, endDate
 */
router.get('/my-lessons',
  isAuthenticated(),
  getMyScheduledLessons
);

/**
 * PUT /api/scheduling/lessons/:bookingId/reschedule
 * Reschedule a lesson to a different time slot
 */
router.put('/lessons/:bookingId/reschedule',
  withRequestBody(),
  isAuthenticated(),
  rescheduleLesson
);

/**
 * GET /api/scheduling/lessons/:bookingId
 * Get detailed information about a specific booking
 */
router.get('/lessons/:bookingId',
  isAuthenticated(),
  getBookingDetails
);

// ============================================================================
// TUTOR ROUTES - View and manage their schedule
// ============================================================================

/**
 * GET /api/scheduling/tutor/schedule
 * Get tutor's schedule (their bookings)
 * Query params: status, page, limit, startDate, endDate, calendarId
 */
router.get('/tutor/schedule',
  isAuthenticated(),
  validateTutorOwnership,
  getTutorSchedule
);

/**
 * GET /api/scheduling/tutor/lessons/:bookingId
 * Get detailed information about a specific booking (tutor view)
 */
router.get('/tutor/lessons/:bookingId',
  isAuthenticated(),
  validateTutorOwnership,
  getBookingDetails
);

export default router;
