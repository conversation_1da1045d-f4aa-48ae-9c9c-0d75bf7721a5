import { Router } from 'express';
import {
  createEvent,
  getEventsByCalendar,
  updateEvent,
  deleteEvent,
  getMyEvents,
  bulkCreateEvents
} from '../controllers/eventController';
import { isAuthenticated } from '../middlewares/auth';
import { validateTutorOwnership, validateSubscriptionForViewing } from '../middlewares/subscriptionValidation';
import { withRequestBody } from '../middlewares/misc';

const router = Router();

// ============================================================================
// TUTOR ROUTES - Manage events/time slots
// ============================================================================

/**
 * POST /api/events
 * Create a new event/time slot
 */
router.post('/',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  createEvent
);

/**
 * POST /api/events/bulk
 * Create multiple events at once
 */
router.post('/bulk',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  bulkCreateEvents
);

/**
 * GET /api/events/my
 * Get tutor's own events
 * Query params: calendarId, status, startDate, endDate, page, limit
 */
router.get('/my',
  isAuthenticated(),
  validateTutorOwnership,
  getMyEvents
);

/**
 * PUT /api/events/:id
 * Update an event
 */
router.put('/:id',
  withRequestBody(),
  isAuthenticated(),
  validateTutorOwnership,
  updateEvent
);

/**
 * DELETE /api/events/:id
 * Delete an event
 */
router.delete('/:id',
  isAuthenticated(),
  validateTutorOwnership,
  deleteEvent
);

// ============================================================================
// PUBLIC/STUDENT ROUTES - View events
// ============================================================================

/**
 * GET /api/events/calendar/:calendarId
 * Get events by calendar (public access for students to view available slots)
 * Query params: startDate, endDate, status, includeBooked
 */
router.get('/calendar/:calendarId',
  isAuthenticated(),
  validateSubscriptionForViewing, // Adds subscription info but doesn't block access
  getEventsByCalendar
);

export default router;
