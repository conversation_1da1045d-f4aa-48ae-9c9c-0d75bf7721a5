import { Router } from "express";
import { isDevMode, withRequestBody } from "../middlewares/misc";
import { sendTestMail, submitComplaint } from "../controllers/misc";
import { isAuthenticated } from "../middlewares/auth";

const miscRouter = Router();

const devRouter = Router({ mergeParams: true });

devRouter.use(isDevMode);

devRouter.post("/send-mail", withRequestBody(), sendTestMail);

miscRouter.use("/dev", devRouter);

miscRouter.post(
  "/create-ticket",
  withRequestBody(),
  isAuthenticated(),
  submitComplaint
);

export default miscRouter;
