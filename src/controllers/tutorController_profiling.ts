import { Request, Response } from "express";
import <PERSON><PERSON> from "../models/tutor";
import { FilterQuery } from "mongoose";

// Helper for consistent error handling
const handleError = (res: Response, error: unknown) => {
  if (error instanceof Error) {
    res.status(500).json({ error: error.message });
  } else {
    res.status(500).json({ error: "An unexpected error occurred" });
  }
};




// Get All Tutors with Filters, Pagination, and Sorting
export const getTutors = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      subject,
      experienceLevel,
      minPrice,
      maxPrice,
      city,
      state,
      rating,
      gender,
      language,
      availability,
      name,
      page = "1",
      limit = "10",
      sortBy = "createdAt",
      order = "desc",
    } = req.query as Record<string, string>;

    const pageNumber = Math.max(parseInt(page), 1); // Ensures at least page 1
    const limitNumber = Math.max(parseInt(limit), 1); // Ensures at least 1 item per page
    const skip = (pageNumber - 1) * limitNumber;

    const filter: FilterQuery<any> = {};

    // Subject title filter
    if (subject) {
      filter["teachingSubjects.title"] = { $regex: subject, $options: "i" };
    }

    // Experience Level filter
    if (experienceLevel) {
      filter["teachingSubjects.experienceLevel"] = experienceLevel;
    }

    // Price Range filter
    if (minPrice || maxPrice) {
      filter.basePrice = {};
      if (minPrice) filter.basePrice.$gte = Number(minPrice);
      if (maxPrice) filter.basePrice.$lte = Number(maxPrice);
    }

    // City filter
    if (city) {
      filter.city = { $regex: city, $options: "i" };
    }

    // State filter
    if (state) {
      filter.state = { $regex: state, $options: "i" };
    }

    // Rating filter
    if (rating) {
      filter.rating = { $gte: Number(rating) };
    }

    // Gender filter
    if (gender) {
      filter.gender = gender;
    }

    // Language filter
    if (language) {
      filter.languages = { $in: [language] };
    }

    // Availability filter
    if (availability) {
      filter.availability = { $regex: availability, $options: "i" };
    }

    // Name search filter (firstName or lastName)
    if (name) {
      filter.$or = [
        { firstName: { $regex: name, $options: "i" } },
        { lastName: { $regex: name, $options: "i" } },
      ];
    }

    const totalTutors = await Tutor.countDocuments(filter);

    const tutors = await Tutor.find(filter)
      .sort({ [sortBy]: order === "asc" ? 1 : -1 })
      .skip(skip)
      .limit(limitNumber);

    res.status(200).json({
      success: true,
      message: "Tutors fetched successfully",
      meta: {
        totalTutors,
        totalPages: Math.ceil(totalTutors / limitNumber),
        currentPage: pageNumber,
        limit: limitNumber,
        sortBy,
        sortOrder: order,
      },
      data: tutors,
    });
  } catch (error: unknown) {
    handleError(res, error);
  }
};


// Get Single Tutor by ID
export const getTutorBy_Id = async (req: Request, res: Response): Promise<any> => {
  try {
    const tutor = await Tutor.findById(req.params.id);
    if (!tutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }
    res.status(200).json(tutor);
  } catch (error: unknown) {
    handleError(res, error);
  }
};

// Update Tutor by ID
export const updateTutor = async (req: Request, res: Response): Promise<any> => {
  try {
    const updatedTutor = await Tutor.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });
    if (!updatedTutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }
    res.status(200).json(updatedTutor);
  } catch (error: unknown) {
    handleError(res, error);
  }
};

// Delete Tutor by ID
export const deleteTutor = async (req: Request, res: Response): Promise<any> => {
  try {
    const deletedTutor = await Tutor.findByIdAndDelete(req.params.id);
    if (!deletedTutor) {
      return res.status(404).json({ message: "Tutor not found" });
    }
    res.status(200).json({ message: "Tutor deleted successfully" });
  } catch (error: unknown) {
    handleError(res, error);
  }
};
