import { Response } from 'express';
import { Types } from 'mongoose';
import { Event } from '../models/Event';
import { Calendar } from '../models/calender';
import { Booking } from '../models/Booking';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

// Create event/time slot (only tutor who owns the calendar)
export const createEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create events', 403);
      return;
    }

    const {
      calendarId,
      title,
      description,
      location,
      startDateTime,
      endDateTime,
      allDay = false,
      visibility = 'public',
      priority = 3,
      bookingInfo = {},
      isRecurring = false,
      recurringPattern
    } = req.body;

    // Validate required fields
    if (!calendarId || !title || !startDateTime || !endDateTime) {
      createErrorResponse(res, 'calendarId, title, startDateTime, and endDateTime are required', 400);
      return;
    }

    if (!Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    // Validate that the calendar exists and belongs to the tutor
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this calendar', 403);
      return;
    }

    // Validate date/time
    const start = new Date(startDateTime);
    const end = new Date(endDateTime);

    if (start >= end) {
      createErrorResponse(res, 'End time must be after start time', 400);
      return;
    }

    if (start <= new Date()) {
      createErrorResponse(res, 'Cannot create events in the past', 400);
      return;
    }

    // Check for overlapping events
    const overlappingEvents = await Event.find({
      tutorId: req.user._id,
      $or: [
        {
          startDateTime: { $lt: end },
          endDateTime: { $gt: start }
        }
      ],
      status: { $ne: 'cancelled' }
    });

    if (overlappingEvents.length > 0) {
      createErrorResponse(res, 'This time slot overlaps with an existing event', 409);
      return;
    }

    // Create event data
    const eventData = {
      calendarId: new Types.ObjectId(calendarId),
      tutorId: req.user._id,
      title,
      description,
      location,
      startDateTime: start,
      endDateTime: end,
      allDay,
      status: 'available',
      visibility,
      priority,
      isRecurring,
      recurringPattern: isRecurring ? recurringPattern : undefined,
      bookingInfo: {
        maxStudents: bookingInfo.maxStudents || 1,
        currentBookings: 0,
        requiresApproval: bookingInfo.requiresApproval || false,
        price: bookingInfo.price || undefined
      }
    };

    // Create single event
    const event = new Event(eventData);
    await event.save();

    const populatedEvent = await Event.findById(event._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      data: populatedEvent
    });

  } catch (error) {
    console.error('Error creating event:', error);
    createErrorResponse(res, 'Failed to create event', 500);
  }
};


// Get events by calendar (for students or tutors)
export const getEventsByCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const calendarId = req.params.calendarId;
    const { startDate, endDate, status, includeBooked = false } = req.query;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    // Check if calendar exists and is accessible
    const calendar = await Calendar.findById(calendarId)
      .populate('tutorId', 'firstname lastname email');

    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    // Check access permissions
    const isOwner = req.user && (req.user._id as any).toString() === calendar.tutorId._id.toString();
    const isSharedAndActive = calendar.isShared && calendar.isActive;

    if (!isOwner && !isSharedAndActive) {
      createErrorResponse(res, 'Calendar is not accessible', 403);
      return;
    }

    // Build query
    let query: any = { calendarId: new Types.ObjectId(calendarId) };

    // If user is student, show only public events
    if (req.user && req.user.role === 'student' && !isOwner) {
      query.visibility = 'public';

      // For students, only show available events unless they specifically want to see booked ones
      if (includeBooked !== 'true') {
        query.status = 'available';
      }
    }

    // Date range filter
    if (startDate || endDate) {
      query.startDateTime = {};
      if (startDate) query.startDateTime.$gte = new Date(startDate as string);
      if (endDate) query.startDateTime.$lte = new Date(endDate as string);
    }

    // Status filter
    if (status && ['available', 'booked', 'cancelled', 'completed'].includes(status as string)) {
      query.status = status;
    }

    const events = await Event.find(query)
      .populate('calendarId', 'name color')
      .sort({ startDateTime: 1 });

    // For students, add booking eligibility info
    let eventsWithEligibility: any[] = events;
    if (req.user && req.user.role === 'student' && !isOwner) {
      // This would be populated by middleware if needed
      eventsWithEligibility = events.map(event => ({
        ...event.toObject(),
        canBook: event.status === 'available' &&
                event.startDateTime > new Date() &&
                (event.bookingInfo?.currentBookings || 0) < (event.bookingInfo?.maxStudents || 1)
      }));
    }

    res.json({
      success: true,
      data: {
        calendar: {
          _id: calendar._id,
          name: calendar.name,
          color: calendar.color,
          tutor: calendar.tutorId
        },
        events: eventsWithEligibility
      }
    });

  } catch (error) {
    console.error('Error fetching events by calendar:', error);
    createErrorResponse(res, 'Failed to fetch events', 500);
  }
};

// Update event (only tutor who created it)
export const updateEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update events', 403);
      return;
    }

    const eventId = req.params.id;

    if (!eventId || !Types.ObjectId.isValid(eventId)) {
      createErrorResponse(res, 'Invalid event ID format', 400);
      return;
    }

    const event = await Event.findById(eventId);
    if (!event) {
      createErrorResponse(res, 'Event not found', 404);
      return;
    }

    if (event.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to update this event', 403);
      return;
    }

    // Check if event has bookings
    const hasBookings = await Booking.countDocuments({
      eventId: event._id,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (hasBookings > 0) {
      createErrorResponse(res, 'Cannot update event with existing bookings. Cancel bookings first.', 400);
      return;
    }

    const {
      title,
      description,
      location,
      startDateTime,
      endDateTime,
      allDay,
      visibility,
      priority,
      bookingInfo
    } = req.body;

    // Validate date/time if being updated
    if (startDateTime || endDateTime) {
      const start = startDateTime ? new Date(startDateTime) : event.startDateTime;
      const end = endDateTime ? new Date(endDateTime) : event.endDateTime;

      if (start >= end) {
        createErrorResponse(res, 'End time must be after start time', 400);
        return;
      }

      if (start <= new Date()) {
        createErrorResponse(res, 'Cannot set event time in the past', 400);
        return;
      }

      // Check for overlapping events (excluding current event)
      const overlappingEvents = await Event.find({
        _id: { $ne: event._id },
        tutorId: req.user._id,
        $or: [
          {
            startDateTime: { $lt: end },
            endDateTime: { $gt: start }
          }
        ],
        status: { $ne: 'cancelled' }
      });

      if (overlappingEvents.length > 0) {
        createErrorResponse(res, 'Updated time slot overlaps with an existing event', 409);
        return;
      }
    }

    // Update fields
    if (title !== undefined) event.title = title;
    if (description !== undefined) event.description = description;
    if (location !== undefined) event.location = location;
    if (startDateTime !== undefined) event.startDateTime = new Date(startDateTime);
    if (endDateTime !== undefined) event.endDateTime = new Date(endDateTime);
    if (allDay !== undefined) event.allDay = allDay;
    if (visibility !== undefined) event.visibility = visibility;
    if (priority !== undefined) event.priority = priority;

    if (bookingInfo) {
      event.bookingInfo = {
        ...event.bookingInfo,
        ...bookingInfo
      };
    }

    await event.save();

    const updatedEvent = await Event.findById(event._id)
      .populate('calendarId', 'name color')
      .populate('tutorId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Event updated successfully',
      data: updatedEvent
    });

  } catch (error) {
    console.error('Error updating event:', error);
    createErrorResponse(res, 'Failed to update event', 500);
  }
};

// Delete event (only tutor who created it)
export const deleteEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can delete events', 403);
      return;
    }

    const eventId = req.params.id;

    if (!eventId || !Types.ObjectId.isValid(eventId)) {
      createErrorResponse(res, 'Invalid event ID format', 400);
      return;
    }

    const event = await Event.findById(eventId);
    if (!event) {
      createErrorResponse(res, 'Event not found', 404);
      return;
    }

    if (event.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to delete this event', 403);
      return;
    }

    // Check if event has active bookings
    const activeBookings = await Booking.find({
      eventId: event._id,
      status: { $in: ['pending', 'confirmed'] }
    }).populate('studentId', 'firstname lastname email');

    if (activeBookings.length > 0) {
      createErrorResponse(res, `Cannot delete event with ${activeBookings.length} active booking(s). Cancel bookings first.`, 400);
      return;
    }

    await event.deleteOne();

    res.json({
      success: true,
      message: 'Event deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting event:', error);
    createErrorResponse(res, 'Failed to delete event', 500);
  }
};

// Get tutor's events
export const getMyEvents = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their events', 403);
      return;
    }

    const {
      calendarId,
      status,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { tutorId: req.user._id };

    if (calendarId && Types.ObjectId.isValid(calendarId as string)) {
      query.calendarId = new Types.ObjectId(calendarId as string);
    }

    if (status && ['available', 'booked', 'cancelled', 'completed'].includes(status as string)) {
      query.status = status;
    }

    // Date range filter
    if (startDate || endDate) {
      query.startDateTime = {};
      if (startDate) query.startDateTime.$gte = new Date(startDate as string);
      if (endDate) query.startDateTime.$lte = new Date(endDate as string);
    }

    const [events, total] = await Promise.all([
      Event.find(query)
        .populate('calendarId', 'name color')
        .sort({ startDateTime: 1 })
        .skip(skip)
        .limit(Number(limit)),
      Event.countDocuments(query)
    ]);

    // Add booking count for each event
    const eventsWithBookings = await Promise.all(
      events.map(async (event) => {
        const bookingCount = await Booking.countDocuments({
          eventId: event._id,
          status: { $in: ['pending', 'confirmed'] }
        });

        return {
          ...event.toObject(),
          actualBookings: bookingCount,
          isFullyBooked: bookingCount >= (event.bookingInfo?.maxStudents || 1),
          canAcceptBookings: event.status === 'available' &&
                           event.startDateTime > new Date() &&
                           bookingCount < (event.bookingInfo?.maxStudents || 1)
        };
      })
    );

    res.json({
      success: true,
      data: eventsWithBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching tutor events:', error);
    createErrorResponse(res, 'Failed to fetch events', 500);
  }
};

// Bulk create events (for creating multiple time slots at once)
export const bulkCreateEvents = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create events', 403);
      return;
    }

    const { calendarId, events: eventsList } = req.body;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    if (!eventsList || !Array.isArray(eventsList) || eventsList.length === 0) {
      createErrorResponse(res, 'Events list is required and must be a non-empty array', 400);
      return;
    }

    // Validate calendar ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'You do not own this calendar', 403);
      return;
    }

    const createdEvents = [];
    const errors = [];

    for (let i = 0; i < eventsList.length; i++) {
      try {
        const eventData = eventsList[i];

        // Validate required fields
        if (!eventData.title || !eventData.startDateTime || !eventData.endDateTime) {
          errors.push(`Event ${i + 1}: title, startDateTime, and endDateTime are required`);
          continue;
        }

        const start = new Date(eventData.startDateTime);
        const end = new Date(eventData.endDateTime);

        if (start >= end) {
          errors.push(`Event ${i + 1}: End time must be after start time`);
          continue;
        }

        if (start <= new Date()) {
          errors.push(`Event ${i + 1}: Cannot create events in the past`);
          continue;
        }

        // Check for overlapping events
        const overlappingEvents = await Event.find({
          tutorId: req.user._id,
          $or: [
            {
              startDateTime: { $lt: end },
              endDateTime: { $gt: start }
            }
          ],
          status: { $ne: 'cancelled' }
        });

        if (overlappingEvents.length > 0) {
          errors.push(`Event ${i + 1}: Time slot overlaps with existing event`);
          continue;
        }

        // Create event
        const event = new Event({
          calendarId: new Types.ObjectId(calendarId),
          tutorId: req.user._id,
          title: eventData.title,
          description: eventData.description,
          location: eventData.location,
          startDateTime: start,
          endDateTime: end,
          allDay: eventData.allDay || false,
          status: 'available',
          visibility: eventData.visibility || 'public',
          priority: eventData.priority || 3,
          bookingInfo: {
            maxStudents: eventData.bookingInfo?.maxStudents || 1,
            currentBookings: 0,
            requiresApproval: eventData.bookingInfo?.requiresApproval || false,
            price: eventData.bookingInfo?.price || undefined
          }
        });

        await event.save();
        createdEvents.push(event);

      } catch (error) {
        errors.push(`Event ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    res.status(201).json({
      success: true,
      message: `Created ${createdEvents.length} events successfully`,
      data: {
        createdEvents,
        errors: errors.length > 0 ? errors : undefined,
        summary: {
          total: eventsList.length,
          created: createdEvents.length,
          failed: errors.length
        }
      }
    });

  } catch (error) {
    console.error('Error bulk creating events:', error);
    createErrorResponse(res, 'Failed to create events', 500);
  }
};
