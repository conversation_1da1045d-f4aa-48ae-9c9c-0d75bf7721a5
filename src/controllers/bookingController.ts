import { Response } from 'express';
import { Types } from 'mongoose';
import { Booking } from '../models/Booking';
import { Event } from '../models/Event';
import Student from '../models/student';
import Subscription from '../models/subscription.model';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

// Student creates a booking request for an event/session
export const createBooking = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    // Validation is handled by middleware, but double-check
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can create bookings', 403);
      return;
    }

    const { eventId, bookingNotes } = req.body;

    if (!eventId) {
      createErrorResponse(res, 'Event ID is required', 400);
      return;
    }

    if (!Types.ObjectId.isValid(eventId)) {
      createErrorResponse(res, 'Invalid event ID format', 400);
      return;
    }

    // Get the event with populated calendar and tutor info
    const event = await Event.findById(eventId)
      .populate('calendarId')
      .populate('tutorId', 'firstname lastname email');

    if (!event) {
      createErrorResponse(res, 'Event not found', 404);
      return;
    }

    // Check if event is available for booking
    const now = new Date();
    const isAvailable = event.status === 'available' &&
                       event.startDateTime > now &&
                       (event.bookingInfo?.currentBookings || 0) < (event.bookingInfo?.maxStudents || 1);

    if (!isAvailable) {
      createErrorResponse(res, 'This time slot is not available for booking', 400);
      return;
    }

    // Check if booking already exists for this student and event
    const existingBooking = await Booking.findOne({
      eventId,
      studentId: req.user._id,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingBooking) {
      createErrorResponse(res, 'You already have a booking for this session', 409);
      return;
    }

    // For trial bookings, check if session is 1 hour or less
    if (req.isTrialBooking) {
      const durationMinutes = Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60));
      if (durationMinutes > 60) {
        createErrorResponse(res, 'Free trial is limited to 1-hour sessions only', 400);
        return;
      }
    }

    // Create the booking
    const bookingData: any = {
      eventId: event._id,
      studentId: req.user._id,
      tutorId: event.tutorId,
      status: 'pending',
      bookingNotes
    };

    // Add subscription ID if this is a subscription-based booking
    if (req.subscription) {
      bookingData.subscriptionId = req.subscription._id;
    }

    const booking = new Booking(bookingData);
    await booking.save();

    // Update event booking count
    await Event.findByIdAndUpdate(eventId, {
      $inc: { 'bookingInfo.currentBookings': 1 },
      status: event.bookingInfo?.maxStudents === 1 ? 'booked' : 'available'
    });

    // If this is a trial booking, mark the student as having used their trial
    if (req.isTrialBooking) {
      await Student.findByIdAndUpdate(req.user._id, {
        hasUsedFreeTrial: true
      });
    }

    // If subscription booking, decrement remaining lessons
    if (req.subscription) {
      await Subscription.findByIdAndUpdate(req.subscription._id, {
        $inc: { remainingLessons: -1 }
      });
    }

    // Populate the booking for response
    const populatedBooking = await Booking.findById(booking._id)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email')
      .populate('studentId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: req.isTrialBooking ?
        'Trial booking created successfully! Enjoy your free 1-hour session.' :
        'Booking created successfully',
      data: {
        booking: populatedBooking,
        isTrialBooking: req.isTrialBooking || false,
        remainingLessons: req.subscription?.remainingLessons || 0
      }
    });

  } catch (error) {
    console.error('Error creating booking:', error);
    createErrorResponse(res, 'Failed to create booking', 500);
  }
};

// Tutor gets bookings for their events
export const getBookingsForTutor = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view bookings', 403);
      return;
    }

    const { status, page = 1, limit = 20, startDate, endDate } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    // Build query for tutor's bookings
    const query: any = { tutorId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Date range filter
    if (startDate || endDate) {
      const eventQuery: any = {};
      if (startDate) eventQuery.startDateTime = { $gte: new Date(startDate as string) };
      if (endDate) {
        eventQuery.startDateTime = eventQuery.startDateTime || {};
        eventQuery.startDateTime.$lte = new Date(endDate as string);
      }

      // Get events in date range
      const events = await Event.find({ tutorId: req.user._id, ...eventQuery }).select('_id');
      query.eventId = { $in: events.map(e => e._id) };
    }

    const [bookings, total] = await Promise.all([
      Booking.find(query)
        .populate('studentId', 'firstname lastname email')
        .populate('eventId', 'title startDateTime endDateTime location')
        .populate('subscriptionId', 'planType status')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Booking.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: bookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching tutor bookings:', error);
    createErrorResponse(res, 'Failed to fetch bookings', 500);
  }
};

// Update booking status (only tutor)
export const updateBookingStatus = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update booking status', 403);
      return;
    }

    const bookingId = req.params.id;
    const { status, tutorNotes, cancellationReason } = req.body;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    const booking = await Booking.findById(bookingId)
      .populate('eventId', 'title startDateTime endDateTime tutorId')
      .populate('studentId', 'firstname lastname email');

    if (!booking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Verify tutor owns the event linked to booking
    const event = booking.eventId as any;
    if (event.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to update this booking', 403);
      return;
    }

    if (!['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status)) {
      createErrorResponse(res, 'Invalid status. Must be: pending, confirmed, cancelled, completed, or no_show', 400);
      return;
    }

    // Update booking
    const updateData: any = { status };

    if (tutorNotes) updateData.tutorNotes = tutorNotes;
    if (status === 'cancelled' && cancellationReason) {
      updateData.cancellationReason = cancellationReason;
      updateData.cancelledBy = 'tutor';
    }

    const updatedBooking = await Booking.findByIdAndUpdate(
      bookingId,
      updateData,
      { new: true }
    ).populate('eventId', 'title startDateTime endDateTime location')
     .populate('studentId', 'firstname lastname email')
     .populate('subscriptionId', 'planType status');

    // Update event booking count if cancelled
    if (status === 'cancelled' && booking.status !== 'cancelled') {
      await Event.findByIdAndUpdate(booking.eventId, {
        $inc: { 'bookingInfo.currentBookings': -1 },
        status: 'available'
      });

      // If this was a subscription booking, restore the lesson
      if (booking.subscriptionId) {
        await Subscription.findByIdAndUpdate(booking.subscriptionId, {
          $inc: { remainingLessons: 1 }
        });
      }
    }

    res.json({
      success: true,
      message: `Booking ${status} successfully`,
      data: updatedBooking
    });

  } catch (error) {
    console.error('Error updating booking status:', error);
    createErrorResponse(res, 'Failed to update booking status', 500);
  }
};

// Get student's bookings
export const getStudentBookings = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view their bookings', 403);
      return;
    }

    const { status, page = 1, limit = 20, upcoming = false } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { studentId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Get bookings with populated event data
    let bookingsQuery = Booking.find(query)
      .populate({
        path: 'eventId',
        select: 'title startDateTime endDateTime location',
        match: upcoming === 'true' ? { startDateTime: { $gte: new Date() } } : {}
      })
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('subscriptionId', 'planType status')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    const [bookings, total] = await Promise.all([
      bookingsQuery.exec(),
      Booking.countDocuments(query)
    ]);

    // Filter out bookings where event was null (due to populate match)
    const filteredBookings = bookings.filter(booking => booking.eventId);

    res.json({
      success: true,
      data: filteredBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: upcoming === 'true' ? filteredBookings.length : total,
        pages: Math.ceil((upcoming === 'true' ? filteredBookings.length : total) / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching student bookings:', error);
    createErrorResponse(res, 'Failed to fetch bookings', 500);
  }
};

// Cancel booking (student or tutor)
export const cancelBooking = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const bookingId = req.params.id;
    const { cancellationReason } = req.body;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    const booking = await Booking.findById(bookingId)
      .populate('eventId', 'title startDateTime endDateTime tutorId')
      .populate('studentId', 'firstname lastname email')
      .populate('tutorId', 'firstname lastname email');

    if (!booking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Check authorization
    const isStudent = req.user.role === 'student' && booking.studentId._id.toString() === (req.user._id as Types.ObjectId).toString();
    const isTutor = req.user.role === 'tutor' && (booking.eventId as any).tutorId.toString() === (req.user._id as Types.ObjectId).toString();

    if (!isStudent && !isTutor) {
      createErrorResponse(res, 'Not authorized to cancel this booking', 403);
      return;
    }

    // Check if booking can be cancelled
    if (!['pending', 'confirmed'].includes(booking.status)) {
      createErrorResponse(res, 'This booking cannot be cancelled', 400);
      return;
    }

    // Check cancellation policy (e.g., minimum notice period)
    const event = booking.eventId as any;
    const hoursUntilEvent = (event.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60);

    if (hoursUntilEvent < 24 && req.user.role === 'student') {
      createErrorResponse(res, 'Bookings must be cancelled at least 24 hours in advance', 400);
      return;
    }

    // Update booking
    const updatedBooking = await Booking.findByIdAndUpdate(
      bookingId,
      {
        status: 'cancelled',
        cancellationReason,
        cancelledBy: req.user.role === 'student' ? 'student' : 'tutor',
        cancelledAt: new Date()
      },
      { new: true }
    ).populate('eventId', 'title startDateTime endDateTime location')
     .populate('studentId', 'firstname lastname email')
     .populate('tutorId', 'firstname lastname email');

    // Update event booking count
    await Event.findByIdAndUpdate(booking.eventId, {
      $inc: { 'bookingInfo.currentBookings': -1 },
      status: 'available'
    });

    // If this was a subscription booking, restore the lesson
    if (booking.subscriptionId) {
      await Subscription.findByIdAndUpdate(booking.subscriptionId, {
        $inc: { remainingLessons: 1 }
      });
    }

    res.json({
      success: true,
      message: 'Booking cancelled successfully',
      data: updatedBooking
    });

  } catch (error) {
    console.error('Error cancelling booking:', error);
    createErrorResponse(res, 'Failed to cancel booking', 500);
  }
};
