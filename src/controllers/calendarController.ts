import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

// Create a calendar (only tutor)
export const createCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create calendars', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone
    } = req.body;

    if (!name) {
      createErrorResponse(res, 'Calendar name is required', 400);
      return;
    }

    // Check if tutor already has a calendar with this name
    const existingCalendar = await Calendar.findOne({
      tutorId: req.user._id,
      name: name.trim()
    });

    if (existingCalendar) {
      createErrorResponse(res, 'You already have a calendar with this name', 409);
      return;
    }

    const calendar = new Calendar({
      tutorId: req.user._id,
      name: name.trim(),
      description,
      color: color || '#3B82F6',
      isShared: isShared ?? true,
      timezone: timezone || 'UTC'
    });

    await calendar.save();

    const populatedCalendar = await Calendar.findById(calendar._id)
      .populate('tutorId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Calendar created successfully',
      data: populatedCalendar
    });

  } catch (error) {
    console.error('Error creating calendar:', error);
    createErrorResponse(res, 'Failed to create calendar', 500);
  }
};

// Get all calendars of a tutor (for learner or tutor)
export const getTutorCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const tutorId = req.params.tutorId;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Build query based on user role and permissions
    let query: any = { tutorId: new Types.ObjectId(tutorId) };

    // If not the tutor themselves, only show shared calendars
    if (!req.user || (req.user._id as Types.ObjectId).toString() !== tutorId) {
      query.isShared = true;
      query.isActive = true;
    }

    const calendars = await Calendar.find(query)
      .populate('tutorId', 'firstname lastname email avatar')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: calendars
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};

// Update calendar (only tutor who owns the calendar)
export const updateCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to update this calendar', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone,
      isActive
    } = req.body;

    // Update fields
    if (name !== undefined) calendar.name = name.trim();
    if (description !== undefined) calendar.description = description;
    if (color !== undefined) calendar.color = color;
    if (isShared !== undefined) calendar.isShared = isShared;
    if (timezone !== undefined) calendar.timezone = timezone;
    if (isActive !== undefined) calendar.isActive = isActive;

    await calendar.save();

    const updatedCalendar = await Calendar.findById(calendar._id)
      .populate('tutorId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Calendar updated successfully',
      data: updatedCalendar
    });

  } catch (error) {
    console.error('Error updating calendar:', error);
    createErrorResponse(res, 'Failed to update calendar', 500);
  }
};

// Delete calendar (only tutor who owns the calendar)
export const deleteCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can delete calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to delete this calendar', 403);
      return;
    }

    // Calendar can be deleted directly since we removed events

    await calendar.deleteOne();

    res.json({
      success: true,
      message: 'Calendar deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting calendar:', error);
    createErrorResponse(res, 'Failed to delete calendar', 500);
  }
};

// Get tutor's own calendars
export const getMyCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their calendars', 403);
      return;
    }

    const { includeInactive = false } = req.query;

    const query: any = { tutorId: req.user._id };
    if (!includeInactive) {
      query.isActive = true;
    }

    const calendars = await Calendar.find(query)
      .populate('defaultScheduleId', 'name isActive')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: calendars,
      summary: {
        totalCalendars: calendars.length,
        activeCalendars: calendars.filter(c => c.isActive).length,
        scheduledCalendars: calendars.filter(c => c.hasSchedule).length
      }
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};
