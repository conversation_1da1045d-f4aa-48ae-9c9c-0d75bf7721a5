import { Request, Response } from "express";
import { getProfile, getProfileModelByRole } from "../utils/profile";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";
import { endOfPeriod, startOfPeriod } from "../utils/datetime";
import TransactionModel from "../models/transaction.model";
import LessonModel from "../models/lesson.model";
import Student from "../models/student";
import SubscriptionModel from "../models/subscription.model";
import EscrowModel from "../models/escrow.model";
import { hashPassword } from "../utils/hashing";

// Create a new tutor
export const createTutor = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { firstname, lastname, email, password, phoneno, ...otherFields } = req.body;

    if (!firstname || !lastname || !email || !password) {
      res.status(400).json({
        message: "Firstname, lastname, email, and password are required.",
      });
      return;
    }

    // Check if email already exists
    const existingUser = await getProfile({ email });
    if (existingUser) {
      res.status(400).json({ message: "Email already in use." });
      return;
    }

    // Create new tutor
    const TutorModel = getProfileModelByRole("tutor");
    const hashedPassword = await hashPassword(password);

    const tutorData = {
      firstname,
      lastname,
      email,
      password: hashedPassword,
      phoneno,
      role: "tutor",
      ...otherFields
    };

    const tutor = await TutorModel.create(tutorData);

    res.status(201).json({ success: true, data: tutor });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

export const getAllTutors = async (
  _req: Request,
  res: Response
): Promise<void> => {
  try {
    const TutorModel = getProfileModelByRole("tutor");
    const tutors = await TutorModel.find({}).sort({ createdAt: -1 });

    res.status(200).json({ success: true, data: tutors });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

// Delete a tutor
export const deleteTutor = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id) {
      res.status(400).json({ success: false, message: "Tutor ID is required." });
      return;
    }

    const TutorModel = getProfileModelByRole("tutor");
    const tutor = await TutorModel.findByIdAndDelete(id);

    if (!tutor) {
      res.status(404).json({ success: false, message: "Tutor not found." });
      return;
    }

    res
      .status(200)
      .json({ success: true, message: "Tutor deleted successfully." });
  } catch (error: any) {
    res
      .status(500)
      .json({ success: false, message: error.message || "Server Error" });
  }
};

export const getOverviewInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;

    const periodStart = startOfPeriod(period as string);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalEarnings = await TransactionModel.aggregate([
      { $match: { userId: tutorId, createdAt: { $lte: periodEnd } } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);
    const periodEarnings = await TransactionModel.aggregate([
      { $match: { userId: tutorId, createdAt: { $gte: periodStart, $lte: periodEnd } } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const totalLessons = await LessonModel.countDocuments({
      tutorId,
      date: { $lte: periodEnd },
    });
    const periodLessons = await LessonModel.countDocuments({
      tutorId,
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalActiveStudents = await LessonModel.distinct("studentId", {
      tutorId,
      date: { $lte: periodEnd },
    }).then((ids) => ids.length);

    const periodActiveStudents = await LessonModel.distinct("studentId", {
      tutorId,
      date: { $gte: periodStart, $lte: periodEnd },
    }).then((ids) => ids.length);

    const totalStudents = await Student.countDocuments({
      createdAt: { $lte: periodEnd },
      tutorId,
    });
    const periodNewStudents = await Student.countDocuments({
      createdAt: { $gte: periodStart, $lte: periodEnd },
      tutorId,
    });

    createOkResponse(res, {
      data: {
        earnings: {
          total: totalEarnings[0]?.total || 0,
          value: periodEarnings[0]?.total || 0,
          percentage: (
            ((periodEarnings[0]?.total || 0) / (totalEarnings[0]?.total || 1)) *
            100
          ).toFixed(2),
        },
        lessons: {
          total: totalLessons,
          value: periodLessons,
          percentage: ((periodLessons / (totalLessons || 1)) * 100).toFixed(2),
        },
        activeStudents: {
          total: totalActiveStudents,
          value: periodActiveStudents,
          percentage: (periodActiveStudents / (totalActiveStudents || 1)) * 100,
        },
        newStudents: {
          total: totalStudents,
          value: periodNewStudents,
          percentage: (periodNewStudents / (totalStudents || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getLessonsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalCancelled = await LessonModel.countDocuments({
      tutorId,
      status: "cancelled",
      date: { $lte: periodEnd },
    });
    const periodCancelled = await LessonModel.countDocuments({
      tutorId,
      status: "cancelled",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalScheduled = await LessonModel.countDocuments({
      tutorId,
      status: "scheduled",
      date: { $lte: periodEnd },
    });
    const periodScheduled = await LessonModel.countDocuments({
      tutorId,
      status: "scheduled",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        cancelled: {
          total: totalCancelled,
          value: periodCancelled,
          percentage: (periodCancelled / (totalCancelled || 1)) * 100,
        },
        scheduled: {
          total: totalScheduled,
          value: periodScheduled,
          percentage: (periodScheduled / (totalScheduled || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const getSubscriptionsInsights = async (req: Request, res: Response) => {
  try {
    const { period = "90" } = req.query;
    const days = parseInt(period as string, 10);
    const periodStart = startOfPeriod(days);
    const periodEnd = endOfPeriod();

    const tutorId = req.user.id;

    const totalProfileViews = await Student.aggregate([
      { $match: { tutorId, "profileViews.date": { $lte: periodEnd } } },
      { $unwind: "$profileViews" },
      { $match: { "profileViews.date": { $lte: periodEnd } } },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const periodProfileViews = await Student.aggregate([
      {
        $match: {
          tutorId,
          "profileViews.date": { $gte: periodStart, $lte: periodEnd },
        },
      },
      { $unwind: "$profileViews" },
      {
        $match: { "profileViews.date": { $gte: periodStart, $lte: periodEnd } },
      },
      { $count: "count" },
    ]).then((res) => res[0]?.count || 0);

    const totalTrialLessons = await LessonModel.countDocuments({
      tutorId,
      type: "trial",
      date: { $lte: periodEnd },
    });
    const periodTrialLessons = await LessonModel.countDocuments({
      tutorId,
      type: "trial",
      date: { $gte: periodStart, $lte: periodEnd },
    });

    const totalSubscriptions = await SubscriptionModel.countDocuments({
      tutorId,
      isActive: true,
      startDate: { $lte: periodEnd },
    });

    const periodSubscriptions = await SubscriptionModel.countDocuments({
      tutorId,
      isActive: true,
      startDate: { $gte: periodStart, $lte: periodEnd },
    });

    createOkResponse(res, {
      data: {
        profileViews: {
          total: totalProfileViews,
          value: periodProfileViews,
          percentage: (periodProfileViews / (totalProfileViews || 1)) * 100,
        },
        trialLessons: {
          total: totalTrialLessons,
          value: periodTrialLessons,
          percentage: (periodTrialLessons / (totalTrialLessons || 1)) * 100,
        },
        newSubscriptions: {
          total: totalSubscriptions,
          value: periodSubscriptions,
          percentage: (periodSubscriptions / (totalSubscriptions || 1)) * 100,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

// Get comprehensive tutor dashboard stats
export const getTutorDashboardStats = async (req: Request, res: Response) => {
  try {
    const tutorId = req.user.id;

    // 1. Total Earnings - from transactions (lesson payouts) and escrow (released amounts)
    const totalEarningsFromTransactions = await TransactionModel.aggregate([
      {
        $match: {
          userId: tutorId,
          type: "lesson_payout",
          status: "completed"
        }
      },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    const totalEarningsFromEscrow = await EscrowModel.aggregate([
      {
        $match: {
          tutorId: tutorId,
          status: "released"
        }
      },
      { $group: { _id: null, total: { $sum: "$tutorPayout" } } },
    ]);

    const totalEarnings = (totalEarningsFromTransactions[0]?.total || 0) +
                         (totalEarningsFromEscrow[0]?.total || 0);

    // 2. Total number of lessons (completed lessons)
    const totalLessons = await LessonModel.countDocuments({
      tutorId,
      status: "completed"
    });

    // 3. Total number of students (unique students who have had lessons with this tutor)
    const totalStudents = await LessonModel.distinct("studentId", {
      tutorId
    }).then((ids) => ids.length);

    // 4. Total number of active students (students with active subscriptions to this tutor)
    const totalActiveStudents = await SubscriptionModel.distinct("studentId", {
      tutorId,
      status: "active"
    }).then((ids) => ids.length);

    // 5. Total hours taught (sum of completed lesson durations in hours)
    const totalHoursTaughtResult = await LessonModel.aggregate([
      {
        $match: {
          tutorId: tutorId,
          status: "completed"
        }
      },
      {
        $group: {
          _id: null,
          totalMinutes: { $sum: "$duration" }
        }
      },
    ]);

    const totalHoursTaught = totalHoursTaughtResult[0]?.totalMinutes
      ? Math.round((totalHoursTaughtResult[0].totalMinutes / 60) * 100) / 100 // Convert to hours and round to 2 decimal places
      : 0;

    createOkResponse(res, {
      data: {
        totalEarnings: {
          amount: totalEarnings,
          currency: "USD",
          formatted: `$${(totalEarnings / 100).toFixed(2)}` // Convert from cents to dollars
        },
        totalLessons,
        totalStudents,
        totalActiveStudents,
        totalHoursTaught
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};
