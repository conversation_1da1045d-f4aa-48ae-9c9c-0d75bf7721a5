import { Request, Response } from "express";
import { createZendeskTicket } from "../utils/zendesk";
import ComplaintTicket, {
  COMPLAINT_PROIVDERS,
  DEFAULT_COMPLAINT_PROVIDER,
} from "../models/complaint-ticket";
import { createOkResponse } from "../utils/misc";
import { createErrorResponse } from "../middlewares/errorHandler";
import { sendMail, SendMailParams } from "../utils/mailer";

export const submitComplaint = async (req: Request, res: Response) => {
  const { subject, description, email, provider } = req.body;

  if (!subject || !description || !email) {
    createErrorResponse(res, "Missing fields");
    return;
  }

  if (provider && !COMPLAINT_PROIVDERS.includes(provider)) {
    createErrorResponse(res, {
      message: "Invalid compliant provider",
      details: {
        allowedComplaintProviders: COMPLAINT_PROIVDERS,
      },
    });
    return;
  }

  try {
    const zendeskResponse = await createZendeskTicket(
      subject,
      description,
      req.user
    );

    const zendeskTicketId = zendeskResponse.ticket.id;

    const ticket = new ComplaintTicket({
      subject,
      description,
      requesterEmail: email,
      ticketId: zendeskTicketId,
      provider: provider || DEFAULT_COMPLAINT_PROVIDER,
    });

    await ticket.save();

    createOkResponse(res, {
      message: "Ticket created successfully",
      data: {
        ticketId: zendeskTicketId,
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const sendTestMail = async (req: Request, res: Response) => {
  try {
    const { to, ...rest } = req.body;

    await sendMail(to, {
      subject: "Convolly Mail service",
      html: "Hello world",
      ...rest,
    });

    createOkResponse(res, "Mail sent successfully.");
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};
