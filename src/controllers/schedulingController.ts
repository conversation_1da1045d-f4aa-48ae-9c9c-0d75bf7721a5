import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calender';
import { Event } from '../models/Event';
import { Booking } from '../models/Booking';
import Student from '../models/student';
import Tutor from '../models/tutor';
import Subscription from '../models/subscription.model';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { checkBookingEligibility } from '../middlewares/subscriptionValidation';

// Enhanced booking types
export enum BookingType {
  LESSON = 'lesson',
  INTERVIEW = 'interview',
  CONSULTATION = 'consultation',
  TRIAL = 'trial'
}

export enum RecurrenceFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  BIWEEKLY = 'weekly', // Map biweekly to weekly for compatibility
  MONTHLY = 'monthly'
}

interface RecurrencePattern {
  frequency: RecurrenceFrequency;
  interval: number;
  daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
  endDate?: Date;
  maxOccurrences?: number;
}

/**
 * Enhanced conflict detection for scheduling
 */
const checkSchedulingConflicts = async (
  tutorId: Types.ObjectId,
  studentId: Types.ObjectId,
  startDateTime: Date,
  endDateTime: Date,
  excludeBookingId?: Types.ObjectId
): Promise<{
  hasConflict: boolean;
  conflictType?: 'tutor_busy' | 'student_busy' | 'overlapping_event';
  conflictDetails?: any;
}> => {
  try {
    // Check if tutor has any conflicting events or bookings
    const tutorConflicts = await Promise.all([
      // Check tutor's events that are booked or unavailable
      Event.find({
        tutorId,
        $or: [
          {
            startDateTime: { $lt: endDateTime },
            endDateTime: { $gt: startDateTime }
          }
        ],
        status: { $in: ['booked', 'unavailable'] }
      }),

      // Check tutor's confirmed bookings
      Booking.find({
        tutorId,
        status: { $in: ['confirmed', 'pending'] },
        ...(excludeBookingId && { _id: { $ne: excludeBookingId } })
      }).populate('eventId', 'startDateTime endDateTime')
    ]);

    const [conflictingEvents, tutorBookings] = tutorConflicts;

    // Check for tutor event conflicts
    if (conflictingEvents.length > 0) {
      return {
        hasConflict: true,
        conflictType: 'tutor_busy',
        conflictDetails: conflictingEvents[0]
      };
    }

    // Check for tutor booking conflicts
    const tutorBookingConflict = tutorBookings.find(booking => {
      const event = booking.eventId as any;
      return (
        (startDateTime >= event.startDateTime && startDateTime < event.endDateTime) ||
        (endDateTime > event.startDateTime && endDateTime <= event.endDateTime) ||
        (startDateTime <= event.startDateTime && endDateTime >= event.endDateTime)
      );
    });

    if (tutorBookingConflict) {
      return {
        hasConflict: true,
        conflictType: 'tutor_busy',
        conflictDetails: tutorBookingConflict
      };
    }

    // Check if student has conflicting bookings
    const studentBookings = await Booking.find({
      studentId,
      status: { $in: ['confirmed', 'pending'] },
      ...(excludeBookingId && { _id: { $ne: excludeBookingId } })
    }).populate('eventId', 'startDateTime endDateTime');

    const studentBookingConflict = studentBookings.find(booking => {
      const event = booking.eventId as any;
      return (
        (startDateTime >= event.startDateTime && startDateTime < event.endDateTime) ||
        (endDateTime > event.startDateTime && endDateTime <= event.endDateTime) ||
        (startDateTime <= event.startDateTime && endDateTime >= event.endDateTime)
      );
    });

    if (studentBookingConflict) {
      return {
        hasConflict: true,
        conflictType: 'student_busy',
        conflictDetails: studentBookingConflict
      };
    }

    return { hasConflict: false };

  } catch (error) {
    console.error('Error checking scheduling conflicts:', error);
    return { hasConflict: true, conflictType: 'overlapping_event' };
  }
};

/**
 * Generate recurring time slots based on tutor's availability patterns
 */
const generateRecurringSlots = async (
  tutorId: string,
  startDate: Date,
  endDate: Date,
  duration: number = 60
): Promise<any[]> => {
  try {
    // Get tutor's recurring availability patterns
    const recurringEvents = await Event.find({
      tutorId: new Types.ObjectId(tutorId),
      isRecurring: true,
      status: 'available'
    }).populate('calendarId');

    const generatedSlots = [];

    for (const template of recurringEvents) {
      if (!template.recurringPattern) continue;

      const { frequency, interval, daysOfWeek, endDate: patternEndDate, maxOccurrences } = template.recurringPattern;

      let currentDate = new Date(Math.max(startDate.getTime(), template.startDateTime.getTime()));
      const maxDate = patternEndDate ?
        new Date(Math.min(endDate.getTime(), patternEndDate.getTime())) :
        endDate;

      let occurrenceCount = 0;
      const maxOccurs = maxOccurrences || 50;

      while (currentDate <= maxDate && occurrenceCount < maxOccurs) {
        // Calculate slot times
        const slotStart = new Date(currentDate);
        slotStart.setHours(template.startDateTime.getHours(), template.startDateTime.getMinutes(), 0, 0);

        const slotEnd = new Date(slotStart.getTime() + duration * 60 * 1000);

        // Check if this slot doesn't conflict with existing bookings
        const hasConflict = await Event.findOne({
          tutorId: new Types.ObjectId(tutorId),
          startDateTime: { $lt: slotEnd },
          endDateTime: { $gt: slotStart },
          status: { $in: ['booked', 'unavailable'] }
        });

        if (!hasConflict && slotStart >= startDate && slotStart <= endDate) {
          generatedSlots.push({
            _id: new Types.ObjectId(),
            calendarId: template.calendarId,
            title: `${template.title} (Recurring)`,
            startDateTime: slotStart,
            endDateTime: slotEnd,
            location: template.location,
            bookingInfo: template.bookingInfo,
            isRecurring: true
          });
        }

        // Calculate next occurrence
        switch (frequency) {
          case RecurrenceFrequency.DAILY:
            currentDate.setDate(currentDate.getDate() + interval);
            break;
          case RecurrenceFrequency.WEEKLY:
            if (daysOfWeek && daysOfWeek.length > 0) {
              // Find next day of week
              let nextDay = currentDate.getDay();
              let daysToAdd = 1;

              while (!daysOfWeek.includes((nextDay + daysToAdd) % 7) && daysToAdd <= 7) {
                daysToAdd++;
              }

              if (daysToAdd > 7) {
                currentDate.setDate(currentDate.getDate() + (7 * interval));
              } else {
                currentDate.setDate(currentDate.getDate() + daysToAdd);
              }
            } else {
              currentDate.setDate(currentDate.getDate() + (7 * interval));
            }
            break;
          case RecurrenceFrequency.BIWEEKLY:
            currentDate.setDate(currentDate.getDate() + (14 * interval));
            break;
          case RecurrenceFrequency.MONTHLY:
            currentDate.setMonth(currentDate.getMonth() + interval);
            break;
        }

        occurrenceCount++;
      }
    }

    return generatedSlots;
  } catch (error) {
    console.error('Error generating recurring slots:', error);
    return [];
  }
};

/**
 * Get available time slots for a specific tutor with enhanced conflict detection
 */
export const getAvailableTimeSlots = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { tutorId } = req.params;
    const {
      startDate,
      endDate,
      duration = 60,
      timezone = 'UTC',
      bookingType = BookingType.LESSON,
      includeRecurring = true
    } = req.query;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Validate date range
    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);

    if (start >= end) {
      createErrorResponse(res, 'Start date must be before end date', 400);
      return;
    }

    // Get tutor and validate availability
    const tutor = await Tutor.findById(tutorId);
    if (!tutor) {
      createErrorResponse(res, 'Tutor not found', 404);
      return;
    }

    if (tutor.approvalStatus !== 'approved' || !tutor.isActive) {
      createErrorResponse(res, 'Tutor is not available for booking', 400);
      return;
    }

    // Get tutor's active calendars
    const calendars = await Calendar.find({
      tutorId: new Types.ObjectId(tutorId),
      isActive: true,
      isShared: true
    });

    if (calendars.length === 0) {
      res.json({
        success: true,
        data: {
          tutor: {
            _id: tutor._id,
            firstname: tutor.firstname,
            lastname: tutor.lastname,
            basePrice: tutor.basePrice,
            rating: tutor.rating
          },
          availableSlots: [],
          message: 'Tutor has no available calendars'
        }
      });
      return;
    }

    // Build query for available events
    const eventQuery: any = {
      tutorId: new Types.ObjectId(tutorId),
      startDateTime: { $gte: start, $lte: end },
      status: 'available'
    };

    // Filter by booking type if specified
    if (bookingType !== BookingType.LESSON) {
      eventQuery['metadata.bookingType'] = bookingType;
    }

    // Get available events
    let availableEvents = await Event.find(eventQuery)
      .populate('calendarId', 'name color bookingSettings')
      .sort({ startDateTime: 1 });

    // Filter out events that have conflicts or are fully booked
    const filteredEvents = [];
    for (const event of availableEvents) {
      const currentBookings = event.bookingInfo?.currentBookings || 0;
      const maxStudents = event.bookingInfo?.maxStudents || 1;

      if (currentBookings < maxStudents) {
        // Additional conflict check for robustness
        if (req.user && req.user.role === 'student') {
          const conflictCheck = await checkSchedulingConflicts(
            new Types.ObjectId(tutorId),
            req.user._id as Types.ObjectId,
            event.startDateTime,
            event.endDateTime
          );

          if (!conflictCheck.hasConflict) {
            filteredEvents.push(event);
          }
        } else {
          filteredEvents.push(event);
        }
      }
    }

    // Generate recurring slots if requested
    if (includeRecurring === 'true') {
      const recurringEvents = await generateRecurringSlots(tutorId, start, end, Number(duration));
      filteredEvents.push(...recurringEvents);
    }

    // Check booking eligibility
    let bookingEligibility = null;
    if (req.user && req.user.role === 'student') {
      bookingEligibility = await checkBookingEligibility(
        req.user._id as Types.ObjectId,
        new Types.ObjectId(tutorId)
      );
    }

    // Format response with enhanced information
    const availableSlots = filteredEvents.map(event => ({
      eventId: event._id,
      calendarId: event.calendarId._id,
      calendarName: (event.calendarId as any).name,
      title: event.title,
      startDateTime: event.startDateTime,
      endDateTime: event.endDateTime,
      duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
      location: event.location,
      price: event.bookingInfo?.price || tutor.basePrice,
      maxStudents: event.bookingInfo?.maxStudents || 1,
      currentBookings: event.bookingInfo?.currentBookings || 0,
      availableSpots: (event.bookingInfo?.maxStudents || 1) - (event.bookingInfo?.currentBookings || 0),
      requiresApproval: event.bookingInfo?.requiresApproval || false,
      bookingType: event.metadata?.bookingType || BookingType.LESSON,
      isRecurring: event.isRecurring || false,
      canBook: bookingEligibility?.canBook || false,
      timezone
    }));

    res.json({
      success: true,
      data: {
        tutor: {
          _id: tutor._id,
          firstname: tutor.firstname,
          lastname: tutor.lastname,
          basePrice: tutor.basePrice,
          rating: tutor.rating,
          totalLessons: tutor.totalLessons,
          timezone: 'UTC' // Default timezone, can be enhanced later
        },
        availableSlots,
        bookingEligibility,
        dateRange: { startDate: start, endDate: end },
        timezone,
        filters: {
          bookingType,
          duration: Number(duration),
          includeRecurring
        }
      }
    });

  } catch (error) {
    console.error('Error fetching available time slots:', error);
    createErrorResponse(res, 'Failed to fetch available time slots', 500);
  }
};

/**
 * Enhanced schedule lesson with robust conflict detection and interview support
 */
export const scheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can schedule lessons', 403);
      return;
    }

    const {
      eventId,
      bookingNotes,
      specialRequests,
      preferredCommunicationMethod = 'platform',
      bookingType = BookingType.LESSON,
      recurringBooking = false,
      recurrencePattern
    } = req.body;

    if (!eventId || !Types.ObjectId.isValid(eventId)) {
      createErrorResponse(res, 'Invalid event ID format', 400);
      return;
    }

    // Get the event with populated data
    const event = await Event.findById(eventId)
      .populate('calendarId')
      .populate('tutorId', 'firstname lastname email basePrice');

    if (!event) {
      createErrorResponse(res, 'Time slot not found', 404);
      return;
    }

    // Enhanced availability check
    const now = new Date();
    const isBasicallyAvailable = event.status === 'available' &&
                                event.startDateTime > now &&
                                (event.bookingInfo?.currentBookings || 0) < (event.bookingInfo?.maxStudents || 1);

    if (!isBasicallyAvailable) {
      createErrorResponse(res, 'This time slot is no longer available', 400);
      return;
    }

    // Enhanced conflict detection
    const conflictCheck = await checkSchedulingConflicts(
      event.tutorId._id,
      req.user._id as Types.ObjectId,
      event.startDateTime,
      event.endDateTime
    );

    if (conflictCheck.hasConflict) {
      const conflictMessages = {
        'tutor_busy': 'The tutor is not available at this time due to another commitment',
        'student_busy': 'You have another lesson scheduled at this time',
        'overlapping_event': 'This time slot conflicts with an existing booking'
      };

      createErrorResponse(res, conflictMessages[conflictCheck.conflictType!] || 'Scheduling conflict detected', 409);
      return;
    }

    // Check if student already has a booking for this specific event
    const existingBooking = await Booking.findOne({
      eventId: event._id,
      studentId: req.user._id,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingBooking) {
      createErrorResponse(res, 'You already have a booking for this time slot', 409);
      return;
    }

    // Validate subscription or trial eligibility based on booking type
    if (bookingType === BookingType.INTERVIEW) {
      // Interviews might have different rules - could be free for first interview
      const existingInterviews = await Booking.countDocuments({
        studentId: req.user._id,
        'metadata.bookingType': BookingType.INTERVIEW,
        status: { $in: ['completed', 'confirmed'] }
      });

      if (existingInterviews === 0) {
        // First interview is free
        req.isTrialBooking = true;
      } else if (!req.subscription) {
        createErrorResponse(res, 'Active subscription required for additional interviews', 403);
        return;
      }
    } else {
      // Regular lesson validation
      if (!req.subscription && !req.isTrialBooking) {
        createErrorResponse(res, 'No valid subscription or trial eligibility found', 403);
        return;
      }
    }

    // For trial bookings, ensure it's 1 hour or less
    if (req.isTrialBooking) {
      const durationMinutes = Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60));
      if (durationMinutes > 60) {
        createErrorResponse(res, 'Free trial/interview is limited to 1-hour sessions only', 400);
        return;
      }
    }

    // Advanced booking validation for different types
    const validationResult = await validateBookingRequest(req.user._id as Types.ObjectId, event, bookingType, req.subscription);
    if (!validationResult.isValid) {
      createErrorResponse(res, validationResult.message || 'Validation failed', 400);
      return;
    }

    // Create the booking with enhanced metadata
    const bookingData: any = {
      eventId: event._id,
      studentId: req.user._id,
      tutorId: event.tutorId,
      status: event.bookingInfo?.requiresApproval ? 'pending' : 'confirmed',
      bookingNotes,
      metadata: {
        bookingType,
        specialRequests,
        preferredCommunicationMethod,
        isTrialBooking: req.isTrialBooking || false,
        price: event.bookingInfo?.price || (event.tutorId as any).basePrice,
        recurringBooking,
        recurrencePattern: recurringBooking ? recurrencePattern : undefined,
        scheduledAt: new Date(),
        timezone: req.body.timezone || 'UTC'
      }
    };

    // Add subscription ID if this is a subscription-based booking
    if (req.subscription) {
      bookingData.subscriptionId = req.subscription._id;
    }

    const booking = new Booking(bookingData);
    await booking.save();

    // Handle recurring bookings
    const recurringBookings = [];
    if (recurringBooking && recurrencePattern) {
      try {
        const generatedBookings = await createRecurringBookings(booking, recurrencePattern, event);
        recurringBookings.push(...generatedBookings);
      } catch (error) {
        console.error('Error creating recurring bookings:', error);
        // Continue with single booking even if recurring fails
      }
    }

    // Update event booking count
    await Event.findByIdAndUpdate(eventId, {
      $inc: { 'bookingInfo.currentBookings': 1 },
      status: (event.bookingInfo?.currentBookings || 0) + 1 >= (event.bookingInfo?.maxStudents || 1) ? 'booked' : 'available'
    });

    // Update student/subscription records
    if (req.isTrialBooking) {
      await Student.findByIdAndUpdate(req.user._id, {
        hasUsedFreeTrial: true,
        $inc: { totalTrialLessons: 1 }
      });
    }

    if (req.subscription && bookingType === BookingType.LESSON) {
      await Subscription.findByIdAndUpdate(req.subscription._id, {
        $inc: { remainingLessons: -1 }
      });
    }

    // Get the populated booking for response
    const populatedBooking = await Booking.findById(booking._id)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('studentId', 'firstname lastname email')
      .populate('subscriptionId', 'planType status remainingLessons');

    const responseMessage = getBookingSuccessMessage(bookingType, req.isTrialBooking || false);

    res.status(201).json({
      success: true,
      message: responseMessage,
      data: {
        booking: populatedBooking,
        recurringBookings: recurringBookings.length > 0 ? recurringBookings : undefined,
        bookingType,
        isTrialBooking: req.isTrialBooking || false,
        requiresApproval: event.bookingInfo?.requiresApproval || false,
        remainingLessons: req.subscription?.remainingLessons || 0,
        totalRecurringBookings: recurringBookings.length
      }
    });

  } catch (error) {
    console.error('Error scheduling lesson:', error);
    createErrorResponse(res, 'Failed to schedule lesson', 500);
  }
};

/**
 * Validate booking request based on type and user eligibility
 */
const validateBookingRequest = async (
  studentId: Types.ObjectId,
  event: any,
  bookingType: BookingType,
  subscription?: any
): Promise<{ isValid: boolean; message?: string }> => {
  try {
    switch (bookingType) {
      case BookingType.INTERVIEW:
        // Check if student has already had an interview with this tutor
        const existingInterview = await Booking.findOne({
          studentId,
          tutorId: event.tutorId,
          'metadata.bookingType': BookingType.INTERVIEW,
          status: { $in: ['completed', 'confirmed'] }
        });

        if (existingInterview && !subscription) {
          return { isValid: false, message: 'You have already completed an interview with this tutor. Subscription required for additional sessions.' };
        }
        break;

      case BookingType.CONSULTATION:
        // Consultations might be limited per month
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);

        const consultationsThisMonth = await Booking.countDocuments({
          studentId,
          'metadata.bookingType': BookingType.CONSULTATION,
          createdAt: { $gte: thisMonth },
          status: { $in: ['completed', 'confirmed'] }
        });

        if (consultationsThisMonth >= 2 && !subscription) {
          return { isValid: false, message: 'Maximum 2 consultations per month without subscription.' };
        }
        break;

      case BookingType.TRIAL:
        // Trial validation is handled elsewhere
        break;

      case BookingType.LESSON:
      default:
        // Regular lesson validation
        if (!subscription) {
          return { isValid: false, message: 'Active subscription required for regular lessons.' };
        }
        break;
    }

    return { isValid: true };
  } catch (error) {
    console.error('Error validating booking request:', error);
    return { isValid: false, message: 'Error validating booking request' };
  }
};

/**
 * Create recurring bookings based on pattern
 */
const createRecurringBookings = async (
  originalBooking: any,
  recurrencePattern: RecurrencePattern,
  originalEvent: any
): Promise<any[]> => {
  try {
    const recurringBookings = [];
    const { frequency, interval, daysOfWeek, endDate, maxOccurrences } = recurrencePattern;

    let currentDate = new Date(originalEvent.startDateTime);
    currentDate.setDate(currentDate.getDate() + 7); // Start from next week

    const maxDate = endDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year max
    let occurrenceCount = 0;
    const maxOccurs = maxOccurrences || 12; // Default max 12 occurrences

    while (currentDate <= maxDate && occurrenceCount < maxOccurs) {
      // Find or create corresponding event for this date
      const eventDuration = originalEvent.endDateTime.getTime() - originalEvent.startDateTime.getTime();
      const newEventStart = new Date(currentDate);
      newEventStart.setHours(originalEvent.startDateTime.getHours(), originalEvent.startDateTime.getMinutes(), 0, 0);
      const newEventEnd = new Date(newEventStart.getTime() + eventDuration);

      // Check if there's an available slot at this time
      const availableEvent = await Event.findOne({
        tutorId: originalEvent.tutorId,
        startDateTime: newEventStart,
        endDateTime: newEventEnd,
        status: 'available'
      });

      if (availableEvent) {
        // Check for conflicts
        const conflictCheck = await checkSchedulingConflicts(
          originalEvent.tutorId,
          originalBooking.studentId,
          newEventStart,
          newEventEnd
        );

        if (!conflictCheck.hasConflict) {
          // Create recurring booking
          const recurringBookingData = {
            ...originalBooking.toObject(),
            _id: new Types.ObjectId(),
            eventId: availableEvent._id,
            metadata: {
              ...originalBooking.metadata,
              isRecurring: true,
              parentBookingId: originalBooking._id,
              occurrenceNumber: occurrenceCount + 1
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          delete recurringBookingData.__v;

          const recurringBooking = new Booking(recurringBookingData);
          await recurringBooking.save();

          // Update event booking count
          await Event.findByIdAndUpdate(availableEvent._id, {
            $inc: { 'bookingInfo.currentBookings': 1 }
          });

          recurringBookings.push(recurringBooking);
        }
      }

      // Calculate next occurrence
      switch (frequency) {
        case RecurrenceFrequency.WEEKLY:
          currentDate.setDate(currentDate.getDate() + (7 * interval));
          break;
        case RecurrenceFrequency.BIWEEKLY:
          currentDate.setDate(currentDate.getDate() + (14 * interval));
          break;
        case RecurrenceFrequency.MONTHLY:
          currentDate.setMonth(currentDate.getMonth() + interval);
          break;
        default:
          currentDate.setDate(currentDate.getDate() + (7 * interval));
      }

      occurrenceCount++;
    }

    return recurringBookings;
  } catch (error) {
    console.error('Error creating recurring bookings:', error);
    return [];
  }
};

/**
 * Get appropriate success message based on booking type
 */
const getBookingSuccessMessage = (bookingType: BookingType, isTrial: boolean): string => {
  if (isTrial) {
    return bookingType === BookingType.INTERVIEW
      ? 'Free interview scheduled successfully!'
      : 'Trial lesson scheduled successfully! Enjoy your free session.';
  }

  switch (bookingType) {
    case BookingType.INTERVIEW:
      return 'Interview scheduled successfully!';
    case BookingType.CONSULTATION:
      return 'Consultation scheduled successfully!';
    case BookingType.LESSON:
    default:
      return 'Lesson scheduled successfully!';
  }
};

/**
 * Get student's scheduled lessons
 */
export const getMyScheduledLessons = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view their scheduled lessons', 403);
      return;
    }

    const {
      status,
      page = 1,
      limit = 20,
      upcoming = false,
      startDate,
      endDate
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { studentId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Build event query for date filtering
    let eventQuery: any = {};
    if (upcoming === 'true') {
      eventQuery.startDateTime = { $gte: new Date() };
    } else if (startDate || endDate) {
      eventQuery.startDateTime = {};
      if (startDate) eventQuery.startDateTime.$gte = new Date(startDate as string);
      if (endDate) eventQuery.startDateTime.$lte = new Date(endDate as string);
    }

    // Get bookings with populated event data
    const bookings = await Booking.find(query)
      .populate({
        path: 'eventId',
        select: 'title startDateTime endDateTime location',
        match: eventQuery
      })
      .populate('tutorId', 'firstname lastname email avatar basePrice rating')
      .populate('subscriptionId', 'planType status remainingLessons')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    // Filter out bookings where event was null (due to populate match)
    const filteredBookings = bookings.filter(booking => booking.eventId);

    // Get total count
    const total = await Booking.countDocuments(query);

    // Format response with additional info
    const formattedBookings = filteredBookings.map(booking => {
      const event = booking.eventId as any;
      const tutor = booking.tutorId as any;

      return {
        ...booking.toObject(),
        lessonInfo: {
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
          location: event.location,
          isUpcoming: event.startDateTime > new Date(),
          timeUntilLesson: event.startDateTime > new Date() ?
            Math.floor((event.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60)) : null
        },
        tutorInfo: {
          name: `${tutor.firstname} ${tutor.lastname}`,
          email: tutor.email,
          avatar: tutor.avatar,
          rating: tutor.rating
        },
        isTrialLesson: !booking.subscriptionId
      };
    });

    res.json({
      success: true,
      data: formattedBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: upcoming === 'true' ? filteredBookings.length : total,
        pages: Math.ceil((upcoming === 'true' ? filteredBookings.length : total) / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching scheduled lessons:', error);
    createErrorResponse(res, 'Failed to fetch scheduled lessons', 500);
  }
};

/**
 * Reschedule a lesson to a different time slot
 */
export const rescheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can reschedule their lessons', 403);
      return;
    }

    const { bookingId } = req.params;
    const { newEventId, reason } = req.body;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    if (!newEventId || !Types.ObjectId.isValid(newEventId)) {
      createErrorResponse(res, 'Invalid new event ID format', 400);
      return;
    }

    // Get the current booking
    const currentBooking = await Booking.findById(bookingId)
      .populate('eventId', 'startDateTime endDateTime tutorId')
      .populate('tutorId', 'firstname lastname email');

    if (!currentBooking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Verify ownership
    if (currentBooking.studentId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to reschedule this booking', 403);
      return;
    }

    // Check if booking can be rescheduled
    if (!['pending', 'confirmed'].includes(currentBooking.status)) {
      createErrorResponse(res, 'This booking cannot be rescheduled', 400);
      return;
    }

    // Check rescheduling policy (e.g., minimum notice period)
    const currentEvent = currentBooking.eventId as any;
    const hoursUntilLesson = (currentEvent.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60);

    if (hoursUntilLesson < 24) {
      createErrorResponse(res, 'Lessons must be rescheduled at least 24 hours in advance', 400);
      return;
    }

    // Get the new event
    const newEvent = await Event.findById(newEventId)
      .populate('tutorId', 'firstname lastname email');

    if (!newEvent) {
      createErrorResponse(res, 'New time slot not found', 404);
      return;
    }

    // Verify it's the same tutor
    if (newEvent.tutorId._id.toString() !== currentEvent.tutorId.toString()) {
      createErrorResponse(res, 'Can only reschedule with the same tutor', 400);
      return;
    }

    // Verify new event is available
    const isNewEventAvailable = newEvent.status === 'available' &&
                               newEvent.startDateTime > new Date() &&
                               (newEvent.bookingInfo?.currentBookings || 0) < (newEvent.bookingInfo?.maxStudents || 1);

    if (!isNewEventAvailable) {
      createErrorResponse(res, 'New time slot is not available', 400);
      return;
    }

    // Check for conflicts with student's other bookings
    const conflictingBookings = await Booking.find({
      studentId: req.user._id,
      _id: { $ne: bookingId },
      status: { $in: ['pending', 'confirmed'] }
    }).populate('eventId', 'startDateTime endDateTime');

    const hasConflict = conflictingBookings.some(booking => {
      const bookingEvent = booking.eventId as any;
      return (
        (newEvent.startDateTime >= bookingEvent.startDateTime && newEvent.startDateTime < bookingEvent.endDateTime) ||
        (newEvent.endDateTime > bookingEvent.startDateTime && newEvent.endDateTime <= bookingEvent.endDateTime) ||
        (newEvent.startDateTime <= bookingEvent.startDateTime && newEvent.endDateTime >= bookingEvent.endDateTime)
      );
    });

    if (hasConflict) {
      createErrorResponse(res, 'You have a conflicting booking at the new time', 409);
      return;
    }

    // Update the booking
    currentBooking.eventId = newEvent._id as any;
    currentBooking.status = newEvent.bookingInfo?.requiresApproval ? 'pending' : 'confirmed';
    if (reason) {
      currentBooking.bookingNotes = (currentBooking.bookingNotes || '') + `\n\nRescheduled: ${reason}`;
    }
    await currentBooking.save();

    // Update event booking counts
    await Promise.all([
      // Decrease count for old event
      Event.findByIdAndUpdate(currentEvent._id, {
        $inc: { 'bookingInfo.currentBookings': -1 },
        status: 'available'
      }),
      // Increase count for new event
      Event.findByIdAndUpdate(newEvent._id, {
        $inc: { 'bookingInfo.currentBookings': 1 },
        status: (newEvent.bookingInfo?.currentBookings || 0) + 1 >= (newEvent.bookingInfo?.maxStudents || 1) ? 'booked' : 'available'
      })
    ]);

    // Get updated booking for response
    const updatedBooking = await Booking.findById(bookingId)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email')
      .populate('subscriptionId', 'planType status');

    res.json({
      success: true,
      message: 'Lesson rescheduled successfully',
      data: updatedBooking
    });

  } catch (error) {
    console.error('Error rescheduling lesson:', error);
    createErrorResponse(res, 'Failed to reschedule lesson', 500);
  }
};

/**
 * Get tutor's schedule (for tutors to view their bookings)
 */
export const getTutorSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their schedule', 403);
      return;
    }

    const {
      status,
      page = 1,
      limit = 20,
      startDate,
      endDate,
      calendarId
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { tutorId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Build event query for filtering
    let eventQuery: any = { tutorId: req.user._id };
    if (startDate || endDate) {
      eventQuery.startDateTime = {};
      if (startDate) eventQuery.startDateTime.$gte = new Date(startDate as string);
      if (endDate) eventQuery.startDateTime.$lte = new Date(endDate as string);
    }
    if (calendarId && Types.ObjectId.isValid(calendarId as string)) {
      eventQuery.calendarId = new Types.ObjectId(calendarId as string);
    }

    // Get events in date range first
    const events = await Event.find(eventQuery).select('_id');
    query.eventId = { $in: events.map(e => e._id) };

    const [bookings, total] = await Promise.all([
      Booking.find(query)
        .populate('studentId', 'firstname lastname email avatar')
        .populate({
          path: 'eventId',
          select: 'title startDateTime endDateTime location calendarId',
          populate: {
            path: 'calendarId',
            select: 'name color'
          }
        })
        .populate('subscriptionId', 'planType status')
        .sort({ 'eventId.startDateTime': 1 })
        .skip(skip)
        .limit(Number(limit)),
      Booking.countDocuments(query)
    ]);

    // Format response
    const formattedBookings = bookings.map(booking => {
      const event = booking.eventId as any;
      const student = booking.studentId as any;

      return {
        ...booking.toObject(),
        lessonInfo: {
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
          location: event.location,
          calendar: event.calendarId
        },
        studentInfo: {
          name: `${student.firstname} ${student.lastname}`,
          email: student.email,
          avatar: student.avatar
        },
        isTrialLesson: !booking.subscriptionId
      };
    });

    res.json({
      success: true,
      data: formattedBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching tutor schedule:', error);
    createErrorResponse(res, 'Failed to fetch schedule', 500);
  }
};

/**
 * Get booking details by ID
 */
export const getBookingDetails = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { bookingId } = req.params;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    const booking = await Booking.findById(bookingId)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('tutorId', 'firstname lastname email avatar basePrice rating')
      .populate({
        path: 'eventId',
        select: 'title startDateTime endDateTime location calendarId',
        populate: {
          path: 'calendarId',
          select: 'name color bookingSettings'
        }
      })
      .populate('subscriptionId', 'planType status remainingLessons monthlyPrice');

    if (!booking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Check authorization
    const isStudent = req.user.role === 'student' && booking.studentId._id.toString() === (req.user._id as Types.ObjectId).toString();
    const isTutor = req.user.role === 'tutor' && booking.tutorId._id.toString() === (req.user._id as Types.ObjectId).toString();

    if (!isStudent && !isTutor) {
      createErrorResponse(res, 'Not authorized to view this booking', 403);
      return;
    }

    const event = booking.eventId as any;
    const student = booking.studentId as any;
    const tutor = booking.tutorId as any;

    // Format detailed response
    const bookingDetails = {
      ...booking.toObject(),
      lessonInfo: {
        title: event.title,
        startDateTime: event.startDateTime,
        endDateTime: event.endDateTime,
        duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
        location: event.location,
        calendar: event.calendarId,
        isUpcoming: event.startDateTime > new Date(),
        timeUntilLesson: event.startDateTime > new Date() ?
          Math.floor((event.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60)) : null,
        canBeCancelled: ['pending', 'confirmed'].includes(booking.status) &&
                       (event.startDateTime.getTime() - new Date().getTime()) > (24 * 60 * 60 * 1000), // 24 hours notice
        canBeRescheduled: ['pending', 'confirmed'].includes(booking.status) &&
                         (event.startDateTime.getTime() - new Date().getTime()) > (24 * 60 * 60 * 1000)
      },
      studentInfo: {
        name: `${student.firstname} ${student.lastname}`,
        email: student.email,
        avatar: student.avatar
      },
      tutorInfo: {
        name: `${tutor.firstname} ${tutor.lastname}`,
        email: tutor.email,
        avatar: tutor.avatar,
        basePrice: tutor.basePrice,
        rating: tutor.rating
      },
      isTrialLesson: !booking.subscriptionId,
      subscriptionInfo: booking.subscriptionId || null
    };

    res.json({
      success: true,
      data: bookingDetails
    });

  } catch (error) {
    console.error('Error fetching booking details:', error);
    createErrorResponse(res, 'Failed to fetch booking details', 500);
  }
};



