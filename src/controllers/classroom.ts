import { Request, Response } from "express";
import Classroom from "../models/classroom";
import { createErrorResponse } from "../middlewares/errorHandler";
import { getProfile } from "../utils/profile";
import { generateAgoraChatUserToken, generateRTCToken } from "../utils/hashing";
import { createOkResponse } from "../utils/misc";
import {
  createChatGroup,
  getAndCreateChatUser,
  TCreateChatGrupPayload,
} from "../services/agora";

export const createClassroom = async (req: Request, res: Response) => {
  try {
    // {
    //   action: 'post',
    //   application: '025c2c51-8fb3-44a6-9c67-66063b0dc8fc',
    //   applicationName: '1554976',
    //   data: { groupid: '283559114899457' },
    //   duration: 0,
    //   entities: [],
    //   organization: '411348949',
    //   properties: {},
    //   timestamp: 1749821445869,
    //   uri: 'https://a41.chat.agora.io/411348949/1554976/chatgroups'
    // }

    const body: TCreateChatGrupPayload = req.body;

    const owner = req.user.username;

    const chatGroup = await createChatGroup({
      ...body,
      owner,
    });

    createOkResponse(res, {
      data: {
        chatGroup,
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};

export const updateTimeLog = async (req: Request, res: Response) => {
  try {
    const { sessionId } = req.params;
    const { action } = req.body;
    const userId = req.user?.id;

    const user = await getProfile({ id: req.user!.id });

    if (!user) {
      createErrorResponse(res, "Profile not found", 404);
      return;
    }

    const userRole = user.role;

    const session = await Classroom.findById(sessionId);

    if (!session) {
      createErrorResponse(res, "Session not found.", 404);
      return;
    }

    const now = new Date();

    if (userRole === "tutor" && session.tutor.toString() === userId) {
      if (action === "join") session.tutorJoinedAt = now;
      else if (action === "leave") session.tutorLeftAt = now;
    } else if (
      userRole === "student" &&
      session.learner.toString() === userId
    ) {
      if (action === "join") session.learnerJoinedAt = now;
      else if (action === "leave") session.learnerLeftAt = now;
    } else {
      res
        .status(403)
        .json({ message: "You are not a participant in this session." });
      return;
    }

    await session.save();
    res.status(200).json(session);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Error updating time log." });
  }
};

export const createRTCToken = async (req: Request, res: Response) => {
  try {
    const { channelId } = req.body;
    const { username } = req.user;

    if (!channelId) {
      createErrorResponse(res, "Channel ID is required.", 400);
      return;
    }

    const chatUser = await getAndCreateChatUser({ username });

    const rtcToken = generateRTCToken(channelId, chatUser.id);

    const chatUserToken = generateAgoraChatUserToken(chatUser.id);

    createOkResponse(res, {
      message: "RTC data generated successfully.",
      data: {
        rtcToken,
        chatUserToken,
        chatUser,
        channel: {
          id: channelId,
        },
      },
    });
  } catch (err: any) {
    createErrorResponse(res, err);
  }
};
