import { Request, Response } from "express";
import { getProfile, getProfiles } from "../utils/profile";
import Student from "../models/student";
import { createErrorResponse } from "../middlewares/errorHandler";
import { createOkResponse } from "../utils/misc";

// Create a new learner under a tutor
export const createLearner = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { firstname, lastname, email, password, phoneno, assignedTutor } =
      req.body;

    if (!firstname || !lastname || !email || !password || !assignedTutor) {
      res.status(400).json({
        message:
          "Firstname, lastname, email, password, and assignedTutor are required.",
      });
      return;
    }

    // Check if assigned tutor exists and is a tutor
    const tutor = await getProfile({ _id: assignedTutor, role: "tutor" });
    if (!tutor) {
      res
        .status(404)
        .json({ message: "Assigned tutor not found or not a tutor." });
      return;
    }

    const learner = await tutor.save({
      ...req.body,
      role: "student",
    });

    res.status(201).json(learner);
  } catch (error: any) {
    console.error("Error creating learner:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};

// Get all learners (optionally filter by tutor)
export const getAllLearners = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    const { tutorId } = req.query;

    const query: any = { role: "student" };
    if (tutorId) {
      query.assignedTutor = tutorId;
    }

    const learners = await getProfiles(query);

    res.status(200).json(learners);
  } catch (error: any) {
    console.error("Error fetching learners:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};

// Delete a learner
export const deleteLearner = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    //   const { id } = req.params;
    //   const learner = await getProfileAndDelete({
    //     _id: id,
    //     role: "student",
    //   });
    //   if (!learner) {
    //     res.status(404).json({ message: "Learner not found." });
    //     return;
    //   }
    //   res.status(200).json({ message: "Learner deleted successfully." });
  } catch (error: any) {
    console.error("Error deleting learner:", error);
    res.status(500).json({ message: error.message || "Server Error" });
  }
};
