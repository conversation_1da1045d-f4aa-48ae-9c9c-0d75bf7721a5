import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calendar';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';

export enum CalendarViewType {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
  AGENDA = 'agenda'
}

/**
 * Get calendar view with basic calendar information
 */
export const getCalendarView = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const {
      viewType = CalendarViewType.MONTH,
      startDate,
      endDate,
      timezone = 'UTC',
      calendarIds
    } = req.query;

    // Validate and parse dates
    const start = startDate ? new Date(startDate as string) : getDefaultStartDate(viewType as CalendarViewType);
    const end = endDate ? new Date(endDate as string) : getDefaultEndDate(start, viewType as CalendarViewType);

    // Build calendar query based on user role
    let calendarQuery: any = {};
    if (req.user.role === 'tutor') {
      calendarQuery.tutorId = req.user._id;
    } else {
      // For students, only show shared and active calendars
      calendarQuery.isShared = true;
      calendarQuery.isActive = true;
    }

    // Filter by specific calendars if provided
    if (calendarIds) {
      const ids = Array.isArray(calendarIds) ? calendarIds : [calendarIds];
      calendarQuery._id = { $in: ids.map(id => new Types.ObjectId(id as string)) };
    }

    // Get calendars
    const calendars = await Calendar.find(calendarQuery)
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('defaultScheduleId');

    if (calendars.length === 0) {
      res.json({
        success: true,
        data: {
          calendars: [],
          viewInfo: {
            viewType,
            startDate: start,
            endDate: end,
            timezone
          }
        }
      });
      return;
    }

    // Generate view-specific data structure
    const viewData = generateViewData([], start, end, viewType as CalendarViewType);

    res.json({
      success: true,
      data: {
        calendars: calendars.map(cal => ({
          id: cal._id,
          name: cal.name,
          color: cal.color,
          tutor: cal.tutorId,
          isShared: cal.isShared,
          isActive: cal.isActive,
          hasSchedule: cal.hasSchedule,
          timezone: cal.timezone
        })),
        viewData,
        viewInfo: {
          viewType,
          startDate: start,
          endDate: end,
          timezone
        }
      }
    });

  } catch (error) {
    console.error('Error fetching calendar view:', error);
    createErrorResponse(res, 'Failed to fetch calendar view', 500);
  }
};

/**
 * Get default start date based on view type
 */
function getDefaultStartDate(viewType: CalendarViewType): Date {
  const now = new Date();
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      return new Date(now.getFullYear(), now.getMonth(), 1);
    case CalendarViewType.WEEK:
      const startOfWeek = new Date(now);
      startOfWeek.setDate(now.getDate() - now.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      return startOfWeek;
    case CalendarViewType.DAY:
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      return startOfDay;
    case CalendarViewType.AGENDA:
      return new Date(now);
    default:
      return new Date(now);
  }
}

/**
 * Get default end date based on view type and start date
 */
function getDefaultEndDate(startDate: Date, viewType: CalendarViewType): Date {
  const end = new Date(startDate);
  
  switch (viewType) {
    case CalendarViewType.MONTH:
      end.setMonth(end.getMonth() + 1);
      end.setDate(0); // Last day of the month
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.WEEK:
      end.setDate(end.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.DAY:
      end.setHours(23, 59, 59, 999);
      return end;
    case CalendarViewType.AGENDA:
      end.setDate(end.getDate() + 30); // Next 30 days
      return end;
    default:
      return new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
  }
}

/**
 * Generate view-specific data structure
 */
function generateViewData(_events: any[], startDate: Date, endDate: Date, viewType: CalendarViewType): any {
  switch (viewType) {
    case CalendarViewType.MONTH:
      return generateMonthViewData(startDate, endDate);
    case CalendarViewType.WEEK:
      return generateWeekViewData(startDate);
    case CalendarViewType.DAY:
      return generateDayViewData(startDate);
    case CalendarViewType.AGENDA:
      return generateAgendaViewData(startDate, endDate);
    default:
      return { viewType, startDate, endDate };
  }
}

/**
 * Generate month view data with days grid
 */
function generateMonthViewData(startDate: Date, endDate: Date): any {
  const weeks = [];
  const current = new Date(startDate);

  // Start from the beginning of the week containing the first day of the month
  current.setDate(current.getDate() - current.getDay());

  while (current <= endDate) {
    const week = [];
    for (let i = 0; i < 7; i++) {
      week.push({
        date: new Date(current),
        isCurrentMonth: current.getMonth() === startDate.getMonth(),
        isToday: current.toDateString() === new Date().toDateString()
      });

      current.setDate(current.getDate() + 1);
    }
    weeks.push(week);
  }

  return { weeks };
}

/**
 * Generate week view data with time slots
 */
function generateWeekViewData(startDate: Date): any {
  const days = [];
  const current = new Date(startDate);

  for (let i = 0; i < 7; i++) {
    days.push({
      date: new Date(current),
      isToday: current.toDateString() === new Date().toDateString()
    });

    current.setDate(current.getDate() + 1);
  }

  return { days };
}

/**
 * Generate day view data with hourly time slots
 */
function generateDayViewData(date: Date): any {
  const hours = [];

  for (let hour = 0; hour < 24; hour++) {
    const hourStart = new Date(date);
    hourStart.setHours(hour, 0, 0, 0);

    hours.push({
      hour,
      time: hourStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    });
  }

  return { date, hours };
}

/**
 * Generate agenda view data for date range
 */
function generateAgendaViewData(startDate: Date, endDate: Date): any {
  const agenda = [];
  const current = new Date(startDate);

  while (current <= endDate) {
    agenda.push({
      date: new Date(current),
      isToday: current.toDateString() === new Date().toDateString()
    });

    current.setDate(current.getDate() + 1);
  }

  return { agenda };
}
