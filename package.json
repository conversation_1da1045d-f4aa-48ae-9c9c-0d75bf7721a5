{"name": "convolly-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/server.js", "dev": "ts-node-dev  --respawn --transpile-only server.ts", "build": "tsc", "render:build": "npm install --legacy-peer-deps && npm run build", "create-super-admin": "ts-node src/scripts/createSuperAdmin.ts"}, "repository": {"type": "git", "url": "git+https://github.com/convolly/convolly-backend.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/convolly/convolly-backend/issues"}, "homepage": "https://github.com/convolly/convolly-backend#readme", "dependencies": {"agora-access-token": "^2.0.4", "agora-token": "^2.0.5", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "i18next": "^25.1.3", "i18next-fs-backend": "^2.6.0", "i18next-http-middleware": "^3.7.4", "install": "^0.13.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "nanoid": "^5.1.5", "nodemailer": "^7.0.2", "stripe": "^18.2.1", "socket.io": "^4.8.1", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-mongo-sanitize": "^1.3.2", "@types/express-rate-limit": "^5.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "@types/node": "^22.15.3", "@types/nodemailer": "^6.4.17", "@types/sanitize-html": "^2.15.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}