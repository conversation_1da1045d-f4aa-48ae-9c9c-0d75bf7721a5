// Simple test script for tutor dashboard stats endpoint
// This is a manual test to verify the endpoint works correctly

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:8000'; // Adjust if your server runs on a different port
const TUTOR_ID = 'test-tutor-id'; // Replace with an actual tutor ID from your database

// Test function
async function testTutorDashboardStats() {
  try {
    console.log('🧪 Testing Tutor Dashboard Stats Endpoint...\n');

    // Note: You'll need to replace this with an actual JWT token for a tutor
    // You can get this by:
    // 1. Creating a tutor account
    // 2. Logging in via the /api/auth/login endpoint
    // 3. Using the returned accessToken
    const tutorToken = 'YOUR_TUTOR_JWT_TOKEN_HERE';

    if (tutorToken === 'YOUR_TUTOR_JWT_TOKEN_HERE') {
      console.log('❌ Please replace the tutorToken with an actual JWT token');
      console.log('   Steps to get a token:');
      console.log('   1. Create a tutor account via /api/auth/register');
      console.log('   2. Login via /api/auth/login');
      console.log('   3. Copy the accessToken from the response');
      console.log('   4. Replace the tutorToken variable in this script');
      return;
    }

    const response = await axios.get(
      `${BASE_URL}/api/tutor/${TUTOR_ID}/insight/dashboard-stats`,
      {
        headers: {
          'Authorization': `Bearer ${tutorToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Request successful!');
    console.log('📊 Dashboard Stats Response:');
    console.log(JSON.stringify(response.data, null, 2));

    // Validate response structure
    const { data } = response.data;
    
    console.log('\n🔍 Validating response structure...');
    
    const expectedFields = [
      'totalEarnings',
      'totalLessons', 
      'totalStudents',
      'totalActiveStudents',
      'totalHoursTaught'
    ];

    let allFieldsPresent = true;
    expectedFields.forEach(field => {
      if (data.hasOwnProperty(field)) {
        console.log(`✅ ${field}: ${JSON.stringify(data[field])}`);
      } else {
        console.log(`❌ Missing field: ${field}`);
        allFieldsPresent = false;
      }
    });

    if (allFieldsPresent) {
      console.log('\n🎉 All expected fields are present!');
      
      // Additional validation for totalEarnings structure
      if (data.totalEarnings && 
          typeof data.totalEarnings.amount === 'number' &&
          data.totalEarnings.currency === 'USD' &&
          typeof data.totalEarnings.formatted === 'string') {
        console.log('✅ totalEarnings structure is correct');
      } else {
        console.log('❌ totalEarnings structure is incorrect');
      }
    } else {
      console.log('\n❌ Some expected fields are missing');
    }

  } catch (error) {
    console.log('❌ Test failed!');
    
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Error: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.log('No response received. Is the server running?');
      console.log('Make sure to start the server with: npm run dev');
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Instructions for manual testing
console.log('📋 Manual Testing Instructions:');
console.log('1. Start the server: npm run dev');
console.log('2. Create a tutor account or use an existing one');
console.log('3. Get a JWT token by logging in');
console.log('4. Replace the tutorToken variable in this script');
console.log('5. Replace the TUTOR_ID with an actual tutor ID');
console.log('6. Run this script: node test-dashboard-stats.js\n');

// Run the test
testTutorDashboardStats();
