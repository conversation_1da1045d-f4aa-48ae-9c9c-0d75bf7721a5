# Tutor Dashboard Stats API

## Overview
The Tutor Dashboard Stats endpoint provides comprehensive statistics for tutors to view their performance metrics on their dashboard.

## Endpoint
```
GET /api/tutor/:id/insight/dashboard-stats
```

## Authentication
- **Required**: Yes
- **Role**: Tutor
- **Method**: JWT Bearer Token

## Request Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "totalEarnings": {
      "amount": 15000,
      "currency": "USD", 
      "formatted": "$150.00"
    },
    "totalLessons": 25,
    "totalStudents": 8,
    "totalActiveStudents": 5,
    "totalHoursTaught": 20.83
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `totalEarnings.amount` | Number | Total earnings in cents |
| `totalEarnings.currency` | String | Currency code (always "USD") |
| `totalEarnings.formatted` | String | Formatted earnings in dollars |
| `totalLessons` | Number | Total number of completed lessons |
| `totalStudents` | Number | Total unique students who have had lessons |
| `totalActiveStudents` | Number | Students with active subscriptions |
| `totalHoursTaught` | Number | Total hours taught (rounded to 2 decimal places) |

## Data Sources

### Total Earnings
Calculated from two sources:
1. **Transactions**: Completed lesson payouts from the `Transaction` model
2. **Escrow**: Released tutor payouts from the `Escrow` model

### Total Lessons
Count of lessons with status "completed" for the tutor.

### Total Students
Count of unique students who have had any lessons with the tutor.

### Total Active Students
Count of unique students who currently have active subscriptions with the tutor.

### Total Hours Taught
Sum of duration (in minutes) of all completed lessons, converted to hours and rounded to 2 decimal places.

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized: Missing or invalid authorization header"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Insufficient permissions",
  "details": {
    "required": ["tutor"],
    "current": "student"
  }
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Internal server error"
}
```

## Usage Example

### JavaScript/Axios
```javascript
const response = await axios.get(
  '/api/tutor/123/insight/dashboard-stats',
  {
    headers: {
      'Authorization': `Bearer ${tutorToken}`
    }
  }
);

const stats = response.data.data;
console.log(`Total Earnings: ${stats.totalEarnings.formatted}`);
console.log(`Total Lessons: ${stats.totalLessons}`);
console.log(`Active Students: ${stats.totalActiveStudents}`);
```

### cURL
```bash
curl -X GET \
  'http://localhost:8000/api/tutor/123/insight/dashboard-stats' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json'
```

## Notes
- The tutor ID in the URL path is used for routing, but the actual data is filtered by the authenticated tutor's ID from the JWT token
- All monetary amounts are stored in cents in the database but displayed in dollars in the formatted field
- Hours are calculated from lesson durations and rounded to 2 decimal places for display
- Only completed lessons are counted for statistics
- Active students are determined by having an active subscription status
